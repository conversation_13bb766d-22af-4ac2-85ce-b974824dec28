syntax = "proto3";

import "collections_service.proto";
import "points_service.proto";
import "snapshots_service.proto";

package qdrant;
option csharp_namespace = "Qdrant.Client.Grpc";

service Qdrant {
  rpc HealthCheck (HealthCheckRequest) returns (HealthCheckReply) {}
}

message HealthCheckRequest {}

message HealthCheckReply {
  string title = 1;
  string version = 2;
  optional string commit = 3;
}
