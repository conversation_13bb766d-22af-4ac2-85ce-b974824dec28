[{"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "glob", "kind": 6, "isExtraImport": true, "importPath": "glob", "description": "glob", "detail": "glob", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "sysconfig", "kind": 6, "isExtraImport": true, "importPath": "sysconfig", "description": "sysconfig", "detail": "sysconfig", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "winreg", "kind": 6, "isExtraImport": true, "importPath": "winreg", "description": "winreg", "detail": "winreg", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "SimpleDirectoryReader", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "StorageContext", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Document", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "SentenceSplitter", "importPath": "llama_index.core.node_parser", "description": "llama_index.core.node_parser", "isExtraImport": true, "detail": "llama_index.core.node_parser", "documentation": {}}, {"label": "SentenceSplitter", "importPath": "llama_index.core.node_parser", "description": "llama_index.core.node_parser", "isExtraImport": true, "detail": "llama_index.core.node_parser", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "ji<PERSON>a", "kind": 6, "isExtraImport": true, "importPath": "ji<PERSON>a", "description": "ji<PERSON>a", "detail": "ji<PERSON>a", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "math", "kind": 6, "isExtraImport": true, "importPath": "math", "description": "math", "detail": "math", "documentation": {}}, {"label": "TfidfVectorizer", "importPath": "sklearn.feature_extraction.text", "description": "sklearn.feature_extraction.text", "isExtraImport": true, "detail": "sklearn.feature_extraction.text", "documentation": {}}, {"label": "QdrantVectorStore", "importPath": "llama_index.vector_stores.qdrant", "description": "llama_index.vector_stores.qdrant", "isExtraImport": true, "detail": "llama_index.vector_stores.qdrant", "documentation": {}}, {"label": "BM25Retriever", "importPath": "llama_index.retrievers.bm25", "description": "llama_index.retrievers.bm25", "isExtraImport": true, "detail": "llama_index.retrievers.bm25", "documentation": {}}, {"label": "FastAPI", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "File", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "StaticFiles", "importPath": "fastapi.staticfiles", "description": "fastapi.staticfiles", "isExtraImport": true, "detail": "fastapi.staticfiles", "documentation": {}}, {"label": "HTMLResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "u<PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "u<PERSON><PERSON>", "description": "u<PERSON><PERSON>", "detail": "u<PERSON><PERSON>", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "<PERSON><PERSON>", "kind": 6, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "class Tee:\n    def __init__(self, file):\n        self.f = file\n    def write(self, what):\n        if self.f is not None:\n            try:\n                self.f.write(what.replace(\"\\n\", \"\\r\\n\"))\n            except OSError:\n                pass\n        tee_f.write(what)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_root_hkey", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU\n        return winreg.HKEY_CURRENT_USER", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "create_shortcut", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def create_shortcut(\n    path, description, filename, arguments=\"\", workdir=\"\", iconpath=\"\", iconindex=0\n):\n    import pythoncom\n    from win32com.shell import shell\n    ilink = pythoncom.CoCreateInstance(\n        shell.CLSID_ShellLink,\n        None,\n        pythoncom.CLSCTX_INPROC_SERVER,\n        shell.IID_IShellLink,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_special_folder_path", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_special_folder_path(path_name):\n    from win32com.shell import shell, shellcon\n    for maybe in \"\"\"\n        CSIDL_COMMON_STARTMENU CSIDL_STARTMENU CSIDL_COMMON_APPDATA\n        CSIDL_LOCAL_APPDATA CSIDL_APPDATA CSIDL_COMMON_DESKTOPDIRECTORY\n        CSIDL_DESKTOPDIRECTORY CSIDL_COMMON_STARTUP CSIDL_STARTUP\n        CSIDL_COMMON_PROGRAMS CSIDL_PROGRAMS CSIDL_PROGRAM_FILES_COMMON\n        CSIDL_PROGRAM_FILES CSIDL_FONTS\"\"\".split():\n        if maybe == path_name:\n            csidl = getattr(shellcon, maybe)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "CopyTo", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def CopyTo(desc, src, dest):\n    import win32api\n    import win32con\n    while 1:\n        try:\n            win32api.CopyFile(src, dest, 0)\n            return\n        except win32api.error as details:\n            if details.winerror == 5:  # access denied - user not admin.\n                raise", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "LoadSystemModule", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def LoadSystemModule(lib_dir, modname):\n    # See if this is a debug build.\n    import importlib.machinery\n    import importlib.util\n    suffix = \"_d\" if \"_d.pyd\" in importlib.machinery.EXTENSION_SUFFIXES else \"\"\n    filename = \"%s%d%d%s.dll\" % (\n        modname,\n        sys.version_info.major,\n        sys.version_info.minor,\n        suffix,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "SetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def SetPyKeyVal(key_name, value_name, value):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.CreateKey(root_key, key_name)\n        try:\n            winreg.SetValueEx(my_key, value_name, 0, winreg.REG_SZ, value)\n            if verbose:\n                print(f\"-> {root_key_name}\\\\{key_name}[{value_name}]={value!r}\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "UnsetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def UnsetPyKeyVal(key_name, value_name, delete_key=False):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.OpenKey(root_key, key_name, 0, winreg.KEY_SET_VALUE)\n        try:\n            winreg.DeleteValue(my_key, value_name)\n            if verbose:\n                print(f\"-> DELETE {root_key_name}\\\\{key_name}[{value_name}]\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterCOMObjects", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterCOMObjects(register=True):\n    import win32com.server.register\n    if register:\n        func = win32com.server.register.RegisterClasses\n    else:\n        func = win32com.server.register.UnregisterClasses\n    flags = {}\n    if not verbose:\n        flags[\"quiet\"] = 1\n    for module, klass_name in com_modules:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterHelpFile", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterHelpFile(register=True, lib_dir=None):\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    if register:\n        # Register the .chm help file.\n        chm_file = os.path.join(lib_dir, \"PyWin32.chm\")\n        if os.path.isfile(chm_file):\n            # This isn't recursive, so if 'Help' doesn't exist, we croak\n            SetPyKeyVal(\"Help\", None, None)\n            SetPyKeyVal(\"Help\\\\Pythonwin Reference\", None, chm_file)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterPyt<PERSON><PERSON>", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterPythonwin(register=True, lib_dir=None):\n    \"\"\"Add (or remove) Pythonwin to context menu for python scripts.\n    ??? Should probably also add Edit command for pys files also.\n    Also need to remove these keys on uninstall, but there's no function\n    to add registry entries to uninstall log ???\n    \"\"\"\n    import os\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    classes_root = get_root_hkey()", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_shortcuts_folder", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_shortcuts_folder():\n    if get_root_hkey() == winreg.HKEY_LOCAL_MACHINE:\n        try:\n            fldr = get_special_folder_path(\"CSIDL_COMMON_PROGRAMS\")\n        except OSError:\n            # No CSIDL_COMMON_PROGRAMS on this platform\n            fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")\n    else:\n        # non-admin install - always goes in this user's start menu.\n        fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_system_dir", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_system_dir():\n    import win32api  # we assume this exists.\n    try:\n        import pythoncom\n        import win32process\n        from win32com.shell import shell, shellcon\n        try:\n            if win32process.IsWow64Process():\n                return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEMX86)\n            return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEM)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "fixup_dbi", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def fixup_dbi():\n    # We used to have a dbi.pyd with our .pyd files, but now have a .py file.\n    # If the user didn't uninstall, they will find the .pyd which will cause\n    # problems - so handle that.\n    import win32api\n    import win32con\n    pyd_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi.pyd\")\n    pyd_d_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi_d.pyd\")\n    py_name = os.path.join(os.path.dirname(win32con.__file__), \"dbi.py\")\n    for this_pyd in (pyd_name, pyd_d_name):", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "install", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def install(lib_dir):\n    import traceback\n    # The .pth file is now installed as a regular file.\n    # Create the .pth file in the site-packages dir, and use only relative paths\n    # We used to write a .pth directly to sys.prefix - clobber it.\n    if os.path.isfile(os.path.join(sys.prefix, \"pywin32.pth\")):\n        os.unlink(os.path.join(sys.prefix, \"pywin32.pth\"))\n    # The .pth may be new and therefore not loaded in this session.\n    # Setup the paths just in case.\n    for name in \"win32 win32\\\\lib Pythonwin\".split():", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "uninstall", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def uninstall(lib_dir):\n    # First ensure our system modules are loaded from pywin32_system, so\n    # we can remove the ones we copied...\n    LoadSystemModule(lib_dir, \"pywintypes\")\n    LoadSystemModule(lib_dir, \"pythoncom\")\n    try:\n        RegisterCOMObjects(False)\n    except Exception as why:\n        print(f\"Failed to unregister COM objects: {why}\")\n    try:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verify_destination", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def verify_destination(location: str) -> str:\n    location = os.path.abspath(location)\n    if not os.path.isdir(location):\n        raise argparse.ArgumentTypeError(\n            f'Path \"{location}\" is not an existing directory!'\n        )\n    return location\ndef main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,\n        description=\"\"\"A post-install script for the pywin32 extensions.\n    * Typical usage:\n    > python -m pywin32_postinstall -install\n    * or (shorter but you don't have control over which python environment is used)\n    > pywin32_postinstall -install\n    You need to execute this script, with a '-install' parameter,\n    to ensure the environment is setup correctly to install COM objects, services, etc.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "tee_f", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "tee_f = open(\n    os.path.join(\n        tempfile.gettempdir(),  # Send output somewhere so it can be found if necessary...\n        \"pywin32_postinstall.log\",\n    ),\n    \"w\",\n)\nclass Tee:\n    def __init__(self, file):\n        self.f = file", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stderr", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stderr = <PERSON><PERSON>(sys.stderr)\nsys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stdout", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "com_modules", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "com_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0\n# Verbosity of output messages.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "silent", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "silent = 0\n# Verbosity of output messages.\nverbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verbose", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "verbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "root_key_name", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "root_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "run_test", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()\n    result = subprocess.run(cmd, check=False, cwd=dirname)\n    print(f\"*** Test script '{script}' exited with {result.returncode}\")\n    sys.stdout.flush()\n    if result.returncode:", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "find_and_run", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def find_and_run(possible_locations, extras):\n    for maybe in possible_locations:\n        if os.path.isfile(maybe):\n            run_test(maybe, extras)\n            break\n    else:\n        raise RuntimeError(\n            \"Failed to locate a test script in one of %s\" % possible_locations\n        )\ndef main():", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def main():\n    import argparse\n    code_directories = [project_root] + site_packages\n    parser = argparse.ArgumentParser(\n        description=\"A script to trigger tests in all subprojects of PyWin32.\"\n    )\n    parser.add_argument(\n        \"-no-user-interaction\",\n        default=False,\n        action=\"store_true\",", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))\nsite_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "site_packages", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "site_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "failures", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "failures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "DocumentService", "kind": 6, "importPath": "app.services.document_service", "description": "app.services.document_service", "peekOfCode": "class DocumentService:\n    def __init__(self):\n        self.splitter = SentenceSplitter(\n            chunk_size=700,\n            chunk_overlap=100,\n            lang=\"zh\"  # 中文句子分割\n        )\n    def process_uploaded_file(self, content: str, filename: str) -> List[Dict[str, Any]]:\n        \"\"\"处理上传的文件内容\"\"\"\n        try:", "detail": "app.services.document_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.services.document_service", "description": "app.services.document_service", "peekOfCode": "logger = logging.getLogger(__name__)\nclass DocumentService:\n    def __init__(self):\n        self.splitter = SentenceSplitter(\n            chunk_size=700,\n            chunk_overlap=100,\n            lang=\"zh\"  # 中文句子分割\n        )\n    def process_uploaded_file(self, content: str, filename: str) -> List[Dict[str, Any]]:\n        \"\"\"处理上传的文件内容\"\"\"", "detail": "app.services.document_service", "documentation": {}}, {"label": "SimpleQdrantClient", "kind": 6, "importPath": "app.services.hybrid_search_service", "description": "app.services.hybrid_search_service", "peekOfCode": "class SimpleQdrantClient:\n    \"\"\"简单的Qdrant REST API客户端\"\"\"\n    def __init__(self, url: str):\n        self.url = url.rstrip('/')\n        import requests\n        self.session = requests.Session()\n    def get_collections(self):\n        \"\"\"获取所有集合\"\"\"\n        response = self.session.get(f\"{self.url}/collections\")\n        response.raise_for_status()", "detail": "app.services.hybrid_search_service", "documentation": {}}, {"label": "HybridSearchService", "kind": 6, "importPath": "app.services.hybrid_search_service", "description": "app.services.hybrid_search_service", "peekOfCode": "class HybridSearchService:\n    def __init__(self, qdrant_url: str = \"http://localhost:6333\"):\n        self.qdrant_url = qdrant_url\n        self.client = None\n        self.collection_name = \"hybsearchdoc\"\n        self.tfidf = None\n        self.embed_model = None\n        self.vector_store = None\n        self.index = None\n        self.bm25_retriever = None", "detail": "app.services.hybrid_search_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.services.hybrid_search_service", "description": "app.services.hybrid_search_service", "peekOfCode": "logger = logging.getLogger(__name__)\nclass SimpleQdrantClient:\n    \"\"\"简单的Qdrant REST API客户端\"\"\"\n    def __init__(self, url: str):\n        self.url = url.rstrip('/')\n        import requests\n        self.session = requests.Session()\n    def get_collections(self):\n        \"\"\"获取所有集合\"\"\"\n        response = self.session.get(f\"{self.url}/collections\")", "detail": "app.services.hybrid_search_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.main", "description": "app.main", "peekOfCode": "logger = logging.getLogger(__name__)\napp = FastAPI(title=\"中文文档混合检索系统\", version=\"2.0.0\")\n# 挂载静态文件\napp.mount(\"/static\", StaticFiles(directory=\"static\"), name=\"static\")\n# 导入模型和服务\nfrom .models import (\n    SearchRequest, SearchResponse, SearchResult,\n    StatsResponse, UploadResponse, HealthResponse\n)\nfrom .services.hybrid_search_service import HybridSearchService", "detail": "app.main", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "app.main", "description": "app.main", "peekOfCode": "app = FastAPI(title=\"中文文档混合检索系统\", version=\"2.0.0\")\n# 挂载静态文件\napp.mount(\"/static\", StaticFiles(directory=\"static\"), name=\"static\")\n# 导入模型和服务\nfrom .models import (\n    SearchRequest, SearchResponse, SearchResult,\n    StatsResponse, UploadResponse, HealthResponse\n)\nfrom .services.hybrid_search_service import HybridSearchService\n# 初始化服务", "detail": "app.main", "documentation": {}}, {"label": "search_service", "kind": 5, "importPath": "app.main", "description": "app.main", "peekOfCode": "search_service = HybridSearchService()\*********(\"/\", response_class=HTMLResponse)\nasync def read_root():\n    \"\"\"返回主页\"\"\"\n    try:\n        with open(\"static/index.html\", \"r\", encoding=\"utf-8\") as f:\n            return HTMLResponse(content=f.read())\n    except FileNotFoundError:\n        return HTMLResponse(content=\"\"\"\n        <html>", "detail": "app.main", "documentation": {}}, {"label": "DocumentUpload", "kind": 6, "importPath": "app.models", "description": "app.models", "peekOfCode": "class DocumentUpload(BaseModel):\n    filename: str\n    content: str\nclass SearchRequest(BaseModel):\n    query: str\n    limit: Optional[int] = 5\n    mode: Optional[str] = \"hybrid\"  # \"hybrid\", \"sparse\", \"dense\", \"bm25\"\n    alpha: Optional[float] = 0.5  # 混合搜索中稀疏向量权重\nclass SearchResult(BaseModel):\n    text: str", "detail": "app.models", "documentation": {}}, {"label": "SearchRequest", "kind": 6, "importPath": "app.models", "description": "app.models", "peekOfCode": "class SearchRequest(BaseModel):\n    query: str\n    limit: Optional[int] = 5\n    mode: Optional[str] = \"hybrid\"  # \"hybrid\", \"sparse\", \"dense\", \"bm25\"\n    alpha: Optional[float] = 0.5  # 混合搜索中稀疏向量权重\nclass SearchResult(BaseModel):\n    text: str\n    score: float\n    source: str\n    mode: str", "detail": "app.models", "documentation": {}}, {"label": "SearchResult", "kind": 6, "importPath": "app.models", "description": "app.models", "peekOfCode": "class SearchResult(BaseModel):\n    text: str\n    score: float\n    source: str\n    mode: str\nclass SearchResponse(BaseModel):\n    results: List[SearchResult]\n    total: int\n    query: str\n    mode: str", "detail": "app.models", "documentation": {}}, {"label": "SearchResponse", "kind": 6, "importPath": "app.models", "description": "app.models", "peekOfCode": "class SearchResponse(BaseModel):\n    results: List[SearchResult]\n    total: int\n    query: str\n    mode: str\n    alpha: Optional[float] = None\nclass StatsResponse(BaseModel):\n    collection_name: str\n    points_count: int\n    vectors_count: Optional[int] = None", "detail": "app.models", "documentation": {}}, {"label": "StatsResponse", "kind": 6, "importPath": "app.models", "description": "app.models", "peekOfCode": "class StatsResponse(BaseModel):\n    collection_name: str\n    points_count: int\n    vectors_count: Optional[int] = None\n    vocab_size: int\n    status: str\nclass UploadResponse(BaseModel):\n    message: str\n    file_count: int\n    total_uploaded: int", "detail": "app.models", "documentation": {}}, {"label": "UploadResponse", "kind": 6, "importPath": "app.models", "description": "app.models", "peekOfCode": "class UploadResponse(BaseModel):\n    message: str\n    file_count: int\n    total_uploaded: int\nclass HealthResponse(BaseModel):\n    status: str\n    service: str\n    version: str", "detail": "app.models", "documentation": {}}, {"label": "HealthResponse", "kind": 6, "importPath": "app.models", "description": "app.models", "peekOfCode": "class HealthResponse(BaseModel):\n    status: str\n    service: str\n    version: str", "detail": "app.models", "documentation": {}}, {"label": "test_health", "kind": 2, "importPath": "comprehensive_test", "description": "comprehensive_test", "peekOfCode": "def test_health():\n    \"\"\"测试健康检查\"\"\"\n    print(\"=== 健康检查测试 ===\")\n    try:\n        r = requests.get(f\"{BASE_URL}/health\")\n        print(f\"状态码: {r.status_code}\")\n        print(f\"响应: {r.json()}\")\n        return r.status_code == 200\n    except Exception as e:\n        print(f\"健康检查失败: {e}\")", "detail": "comprehensive_test", "documentation": {}}, {"label": "test_stats", "kind": 2, "importPath": "comprehensive_test", "description": "comprehensive_test", "peekOfCode": "def test_stats():\n    \"\"\"测试统计信息\"\"\"\n    print(\"\\n=== 统计信息测试 ===\")\n    try:\n        r = requests.get(f\"{BASE_URL}/stats\")\n        print(f\"状态码: {r.status_code}\")\n        stats = r.json()\n        print(f\"Collection名称: {stats['collection_name']}\")\n        print(f\"词汇表大小: {stats['vocab_size']}\")\n        print(f\"系统状态: {stats['status']}\")", "detail": "comprehensive_test", "documentation": {}}, {"label": "test_search_modes", "kind": 2, "importPath": "comprehensive_test", "description": "comprehensive_test", "peekOfCode": "def test_search_modes():\n    \"\"\"测试不同搜索模式\"\"\"\n    print(\"\\n=== 搜索模式测试 ===\")\n    test_queries = [\n        (\"深度学习\", \"bm25\"),\n        (\"机器学习算法\", \"hybrid\"),\n        (\"神经网络\", \"sparse\"),\n        (\"人工智能应用\", \"dense\")\n    ]\n    results = []", "detail": "comprehensive_test", "documentation": {}}, {"label": "test_performance", "kind": 2, "importPath": "comprehensive_test", "description": "comprehensive_test", "peekOfCode": "def test_performance():\n    \"\"\"测试性能\"\"\"\n    print(\"\\n=== 性能测试 ===\")\n    query = \"人工智能\"\n    times = []\n    for i in range(5):\n        start_time = time.time()\n        try:\n            data = {\"query\": query, \"limit\": 5, \"mode\": \"bm25\"}\n            r = requests.post(f\"{BASE_URL}/search\", json=data)", "detail": "comprehensive_test", "documentation": {}}, {"label": "test_frontend", "kind": 2, "importPath": "comprehensive_test", "description": "comprehensive_test", "peekOfCode": "def test_frontend():\n    \"\"\"测试前端页面\"\"\"\n    print(\"\\n=== 前端页面测试 ===\")\n    try:\n        r = requests.get(BASE_URL)\n        print(f\"状态码: {r.status_code}\")\n        print(f\"内容长度: {len(r.text)} 字符\")\n        # 检查是否包含关键元素\n        content = r.text.lower()\n        checks = [", "detail": "comprehensive_test", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "comprehensive_test", "description": "comprehensive_test", "peekOfCode": "def main():\n    \"\"\"主测试函数\"\"\"\n    print(\"中文混合检索系统 - 综合测试\")\n    print(\"=\" * 50)\n    tests = [\n        (\"健康检查\", test_health),\n        (\"统计信息\", test_stats),\n        (\"搜索功能\", test_search_modes),\n        (\"性能测试\", test_performance),\n        (\"前端页面\", test_frontend)", "detail": "comprehensive_test", "documentation": {}}, {"label": "BASE_URL", "kind": 5, "importPath": "comprehensive_test", "description": "comprehensive_test", "peekOfCode": "BASE_URL = \"http://localhost:8000\"\ndef test_health():\n    \"\"\"测试健康检查\"\"\"\n    print(\"=== 健康检查测试 ===\")\n    try:\n        r = requests.get(f\"{BASE_URL}/health\")\n        print(f\"状态码: {r.status_code}\")\n        print(f\"响应: {r.json()}\")\n        return r.status_code == 200\n    except Exception as e:", "detail": "comprehensive_test", "documentation": {}}, {"label": "load_environment", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def load_environment():\n    \"\"\"加载环境变量\"\"\"\n    load_dotenv()  # 从 .env 文件加载环境变量\n    # 设置默认值\n    os.environ.setdefault(\"OPENAI_API_KEY\", \"sk-zbruJKss5bHXcDraW0mcHI5xcYhUJ4fi80kxV16IWly1F3Nn\")\n    os.environ.setdefault(\"OPENAI_API_BASE\", \"https://api.openai-proxy.org/v1\")\n    os.environ.setdefault(\"QDRANT_URL\", \"http://localhost:6333\")\n    os.environ.setdefault(\"APP_HOST\", \"0.0.0.0\")\n    os.environ.setdefault(\"APP_PORT\", \"8000\")\ndef check_qdrant():", "detail": "start", "documentation": {}}, {"label": "check_qdrant", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def check_qdrant():\n    \"\"\"检查 Qdrant 是否运行\"\"\"\n    try:\n        import requests\n        qdrant_url = os.getenv(\"QDRANT_URL\", \"http://localhost:6333\")\n        response = requests.get(f\"{qdrant_url}/health\")\n        return response.status_code == 200\n    except:\n        return False\ndef start_qdrant():", "detail": "start", "documentation": {}}, {"label": "start_qdrant", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def start_qdrant():\n    \"\"\"启动 Qdrant\"\"\"\n    print(\"启动 Qdrant...\")\n    subprocess.run([\"docker-compose\", \"up\", \"-d\", \"qdrant\"])\n    # 等待 Qdrant 启动\n    for i in range(30):\n        if check_qdrant():\n            print(\"Qdrant 启动成功!\")\n            return True\n        time.sleep(1)", "detail": "start", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def main():\n    # 加载环境变量\n    load_environment()\n    # 确保必要的目录存在\n    os.makedirs(\"static/css\", exist_ok=True)\n    os.makedirs(\"static/js\", exist_ok=True)\n    os.makedirs(\"app/services\", exist_ok=True)\n    # 检查 OpenAI API 配置\n    api_key = os.getenv(\"OPENAI_API_KEY\")\n    api_base = os.getenv(\"OPENAI_API_BASE\")", "detail": "start", "documentation": {}}, {"label": "test_search", "kind": 2, "importPath": "test_search", "description": "test_search", "peekOfCode": "def test_search(query, mode='bm25', limit=5):\n    data = {\n        'query': query,\n        'limit': limit,\n        'mode': mode\n    }\n    r = requests.post('http://localhost:8000/search', json=data)\n    print(f'Status: {r.status_code}')\n    if r.status_code == 200:\n        response = r.json()", "detail": "test_search", "documentation": {}}]