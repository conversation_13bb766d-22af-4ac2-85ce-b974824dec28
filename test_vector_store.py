#!/usr/bin/env python3
"""测试向量存储问题"""

import sys
import traceback
from app.services.hybrid_search_service import HybridSearchService

def test_vector_store():
    """测试向量存储创建"""
    try:
        print("=== 测试向量存储 ===")
        
        # 创建服务实例
        service = HybridSearchService()
        print(f"✓ 服务创建成功")
        print(f"  客户端类型: {type(service.client)}")
        print(f"  向量存储: {service.vector_store}")
        
        # 测试文档上传
        test_docs = [{
            "filename": "test.txt",
            "content": "这是一个测试文档。人工智能是计算机科学的一个分支。"
        }]
        
        print("\n=== 测试文档上传 ===")
        result = service.add_documents_from_files(test_docs)
        print(f"上传结果: {result}")
        
        # 检查 Qdrant 中的数据
        print("\n=== 检查 Qdrant 数据 ===")
        if hasattr(service.client, 'get_collection'):
            try:
                collection_info = service.client.get_collection(service.collection_name)
                print(f"集合信息: {collection_info}")
            except Exception as e:
                print(f"获取集合信息失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_vector_store()
