import requests
import json

def test_search(query, mode='bm25', limit=5):
    data = {
        'query': query,
        'limit': limit,
        'mode': mode
    }
    r = requests.post('http://localhost:8000/search', json=data)
    print(f'Status: {r.status_code}')
    
    if r.status_code == 200:
        response = r.json()
        print(f'Query: {response["query"]}')
        print(f'Mode: {response["mode"]}')
        print(f'Total results: {response["total"]}')
        print()
        
        for i, result in enumerate(response['results']):
            print(f'Result {i+1}:')
            print(f'  Source: {result["source"]}')
            print(f'  Score: {result["score"]}')
            print(f'  Text preview: {result["text"][:100]}...')
            print()
    else:
        print(f'Error: {r.text}')

if __name__ == "__main__":
    print("=== 测试搜索功能 ===")
    
    print("\n1. 搜索'深度学习':")
    test_search("深度学习")
    
    print("\n2. 搜索'机器学习算法':")
    test_search("机器学习算法")
    
    print("\n3. 搜索'神经网络':")
    test_search("神经网络")
    
    print("\n4. 测试混合搜索模式（应该切换到BM25）:")
    test_search("人工智能应用", mode="hybrid")
