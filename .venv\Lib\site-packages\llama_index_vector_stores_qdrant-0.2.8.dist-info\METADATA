Metadata-Version: 2.1
Name: llama-index-vector-stores-qdrant
Version: 0.2.8
Summary: llama-index vector_stores qdrant integration
License: MIT
Author: Your Name
Author-email: <EMAIL>
Requires-Python: >=3.9,<3.13
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Provides-Extra: fastembed
Requires-Dist: grpcio (>=1.60.0,<2.0.0)
Requires-Dist: llama-index-core (>=0.10.1,<0.11.0)
Requires-Dist: qdrant-client (>=1.7.1,<2.0.0)
Description-Content-Type: text/markdown

# LlamaIndex Vector_Stores Integration: Qdrant

