#!/usr/bin/env python3
"""
中文混合检索系统综合测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("=== 健康检查测试 ===")
    try:
        r = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {r.status_code}")
        print(f"响应: {r.json()}")
        return r.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_stats():
    """测试统计信息"""
    print("\n=== 统计信息测试 ===")
    try:
        r = requests.get(f"{BASE_URL}/stats")
        print(f"状态码: {r.status_code}")
        stats = r.json()
        print(f"Collection名称: {stats['collection_name']}")
        print(f"词汇表大小: {stats['vocab_size']}")
        print(f"系统状态: {stats['status']}")
        return r.status_code == 200
    except Exception as e:
        print(f"统计信息获取失败: {e}")
        return False

def test_search_modes():
    """测试不同搜索模式"""
    print("\n=== 搜索模式测试 ===")
    
    test_queries = [
        ("深度学习", "bm25"),
        ("机器学习算法", "hybrid"),
        ("神经网络", "sparse"),
        ("人工智能应用", "dense")
    ]
    
    results = []
    for query, mode in test_queries:
        try:
            data = {"query": query, "limit": 3, "mode": mode}
            r = requests.post(f"{BASE_URL}/search", json=data)
            
            if r.status_code == 200:
                response = r.json()
                print(f"查询: '{query}' (模式: {mode})")
                print(f"  实际模式: {response['mode']}")
                print(f"  结果数量: {response['total']}")
                print(f"  状态: ✓ 成功")
                results.append(True)
            else:
                print(f"查询: '{query}' (模式: {mode}) - 失败: {r.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"查询: '{query}' (模式: {mode}) - 异常: {e}")
            results.append(False)
    
    return all(results)

def test_performance():
    """测试性能"""
    print("\n=== 性能测试 ===")
    
    query = "人工智能"
    times = []
    
    for i in range(5):
        start_time = time.time()
        try:
            data = {"query": query, "limit": 5, "mode": "bm25"}
            r = requests.post(f"{BASE_URL}/search", json=data)
            end_time = time.time()
            
            if r.status_code == 200:
                response_time = (end_time - start_time) * 1000  # 转换为毫秒
                times.append(response_time)
                print(f"测试 {i+1}: {response_time:.2f}ms")
            else:
                print(f"测试 {i+1}: 失败")
                
        except Exception as e:
            print(f"测试 {i+1}: 异常 - {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"平均响应时间: {avg_time:.2f}ms")
        return avg_time < 1000  # 期望响应时间小于1秒
    
    return False

def test_frontend():
    """测试前端页面"""
    print("\n=== 前端页面测试 ===")
    try:
        r = requests.get(BASE_URL)
        print(f"状态码: {r.status_code}")
        print(f"内容长度: {len(r.text)} 字符")
        
        # 检查是否包含关键元素
        content = r.text.lower()
        checks = [
            "中文文档混合检索" in content,
            "上传文档" in content,
            "搜索" in content,
            "javascript" in content or "script" in content
        ]
        
        print(f"页面内容检查: {sum(checks)}/4 通过")
        return r.status_code == 200 and sum(checks) >= 3
        
    except Exception as e:
        print(f"前端页面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("中文混合检索系统 - 综合测试")
    print("=" * 50)
    
    tests = [
        ("健康检查", test_health),
        ("统计信息", test_stats),
        ("搜索功能", test_search_modes),
        ("性能测试", test_performance),
        ("前端页面", test_frontend)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统运行正常。")
    elif passed >= len(results) * 0.8:
        print("⚠️  大部分测试通过，系统基本正常。")
    else:
        print("❌ 多个测试失败，系统可能存在问题。")

if __name__ == "__main__":
    main()
