syntax = "proto3";

import "points.proto";

package qdrant;
option csharp_namespace = "Qdrant.Client.Grpc";

service Points {
  /*
  Perform insert + updates on points. If a point with a given ID already exists - it will be overwritten.
  */
  rpc Upsert (UpsertPoints) returns (PointsOperationResponse) {}
  /*
  Delete points
  */
  rpc Delete (DeletePoints) returns (PointsOperationResponse) {}
  /*
  Retrieve points
  */
  rpc Get (GetPoints) returns (GetResponse) {}
  /*
  Update named vectors for point
  */
  rpc UpdateVectors (UpdatePointVectors) returns (PointsOperationResponse) {}
  /*
  Delete named vectors for points
  */
  rpc DeleteVectors (DeletePointVectors) returns (PointsOperationResponse) {}
  /*
  Set payload for points
  */
  rpc SetPayload (SetPayloadPoints) returns (PointsOperationResponse) {}
  /*
  Overwrite payload for points
  */
  rpc OverwritePayload (SetPayloadPoints) returns (PointsOperationResponse) {}
  /*
  Delete specified key payload for points
  */
  rpc DeletePayload (DeletePayloadPoints) returns (PointsOperationResponse) {}
  /*
  Remove all payload for specified points
  */
  rpc ClearPayload (ClearPayloadPoints) returns (PointsOperationResponse) {}
  /*
  Create index for field in collection
  */
  rpc CreateFieldIndex (CreateFieldIndexCollection) returns (PointsOperationResponse) {}
  /*
  Delete field index for collection
  */
  rpc DeleteFieldIndex (DeleteFieldIndexCollection) returns (PointsOperationResponse) {}
  /*
  Retrieve closest points based on vector similarity and given filtering conditions
  */
  rpc Search (SearchPoints) returns (SearchResponse) {}
  /*
  Retrieve closest points based on vector similarity and given filtering conditions
  */
  rpc SearchBatch (SearchBatchPoints) returns (SearchBatchResponse) {}
  /*
  Retrieve closest points based on vector similarity and given filtering conditions, grouped by a given field
  */
  rpc SearchGroups (SearchPointGroups) returns (SearchGroupsResponse) {}
  /*
  Iterate over all or filtered points
  */
  rpc Scroll (ScrollPoints) returns (ScrollResponse) {}
  /*
  Look for the points which are closer to stored positive examples and at the same time further to negative examples.
  */
  rpc Recommend (RecommendPoints) returns (RecommendResponse) {}
  /*
  Look for the points which are closer to stored positive examples and at the same time further to negative examples.
  */
  rpc RecommendBatch (RecommendBatchPoints) returns (RecommendBatchResponse) {}
  /*
  Look for the points which are closer to stored positive examples and at the same time further to negative examples, grouped by a given field
  */
  rpc RecommendGroups (RecommendPointGroups) returns (RecommendGroupsResponse) {}
  /*
  Use context and a target to find the most similar points to the target, constrained by the context.

  When using only the context (without a target), a special search - called context search - is performed where
  pairs of points are used to generate a loss that guides the search towards the zone where
  most positive examples overlap. This means that the score minimizes the scenario of
  finding a point closer to a negative than to a positive part of a pair.

  Since the score of a context relates to loss, the maximum score a point can get is 0.0,
  and it becomes normal that many points can have a score of 0.0.

  When using target (with or without context), the score behaves a little different: The
  integer part of the score represents the rank with respect to the context, while the
  decimal part of the score relates to the distance to the target. The context part of the score for
  each pair is calculated +1 if the point is closer to a positive than to a negative part of a pair,
  and -1 otherwise.
  */
  rpc Discover (DiscoverPoints) returns (DiscoverResponse) {}
  /*
  Batch request points based on { positive, negative } pairs of examples, and/or a target
  */
  rpc DiscoverBatch (DiscoverBatchPoints) returns (DiscoverBatchResponse) {}
  /*
  Count points in collection with given filtering conditions
  */
  rpc Count (CountPoints) returns (CountResponse) {}

  /*
  Perform multiple update operations in one request
  */
  rpc UpdateBatch (UpdateBatchPoints) returns (UpdateBatchResponse) {}
  /*
  Universally query points. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
  */
  rpc Query (QueryPoints) returns (QueryResponse) {}
  /*
  Universally query points in a batch fashion. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
  */
  rpc QueryBatch (QueryBatchPoints) returns (QueryBatchResponse) {}
  /*
  Universally query points in a group fashion. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
  */
  rpc QueryGroups (QueryPointGroups) returns (QueryGroupsResponse) {}
}
