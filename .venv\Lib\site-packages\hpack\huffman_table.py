"""
Implementation of a <PERSON><PERSON><PERSON> decoding table for HTTP/2.

This is essentially a Python port of the work originally done for nghttp2's
<PERSON><PERSON><PERSON> decoding. For this reason, while this file is made available under the
MIT license as is the rest of this module, this file is undoubtedly a
derivative work of the nghttp2 file ``nghttp2_hd_huffman_data.c``, obtained
from https://github.com/tatsuhiro-t/nghttp2/ at commit
d2b55ad1a245e1d1964579fa3fac36ebf3939e72. That work is made available under
the Apache 2.0 license under the following terms:

    Copyright (c) 2013 <PERSON><PERSON><PERSON>

    Permission is hereby granted, free of charge, to any person obtaining
    a copy of this software and associated documentation files (the
    "Software"), to deal in the Software without restriction, including
    without limitation the rights to use, copy, modify, merge, publish,
    distribute, sublicense, and/or sell copies of the Software, and to
    permit persons to whom the Software is furnished to do so, subject to
    the following conditions:

    The above copyright notice and this permission notice shall be
    included in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
    LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
    OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
    WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

The essence of this approach is that it builds a finite state machine out of
4-bit nibbles of Huffman coded data. The input function passes 4 bits worth of
data to the state machine each time, which uses those 4 bits of data along with
the current accumulated state data to process the data given.

For the sake of efficiency, the in-memory representation of the states,
transitions, and result values of the state machine are represented as a long
list containing three-tuples. This list is enormously long, and viewing it as
an in-memory representation is not very clear, but it is laid out here in a way
that is intended to be *somewhat* more clear.

Essentially, the list is structured as 256 collections of 16 entries (one for
each nibble) of three-tuples. Each collection is called a "node", and the
zeroth collection is called the "root node". The state machine tracks one
value: the "state" byte.

For each nibble passed to the state machine, it first multiplies the "state"
byte by 16 and adds the numerical value of the nibble. This number is the index
into the large flat list.

The three-tuple that is found by looking up that index consists of three
values:

- a new state value, used for subsequent decoding
- a collection of flags, used to determine whether data is emitted or whether
  the state machine is complete.
- the byte value to emit, assuming that emitting a byte is required.

The flags are consulted, if necessary a byte is emitted, and then the next
nibble is used. This continues until the state machine believes it has
completely Huffman-decoded the data.

This approach has relatively little indirection, and therefore performs
relatively well, particularly on implementations like PyPy where the cost of
loops at the Python-level is not too expensive. The total number of loop
iterations is 4x the number of bytes passed to the decoder.
"""
from __future__ import annotations

from .exceptions import HPACKDecodingError


# This defines the state machine "class" at the top of the file. The reason we
# do this is to keep the terrifing monster state table at the *bottom* of the
# file so you don't have to actually *look* at the damn thing.
def decode_huffman(huffman_string: bytes | bytearray | None) -> bytes:
    """
    Given a bytestring of Huffman-encoded data for HPACK, returns a bytestring
    of the decompressed data.
    """
    if not huffman_string:
        return b""

    state = 0
    flags = 0
    decoded_bytes = bytearray()

    # Perversely, bytearrays are a lot more convenient across Python 2 and
    # Python 3 because they behave *the same way* on both platforms. Given that
    # we really do want numerical bytes when we iterate here, let's use a
    # bytearray.
    huffman_string = bytearray(huffman_string)

    # This loop is unrolled somewhat. Because we use a nibble, not a byte, we
    # need to handle each nibble twice. We unroll that: it makes the loop body
    # a bit longer, but that's ok.
    for input_byte in huffman_string:
        index = (state * 16) + (input_byte >> 4)
        state, flags, output_byte = HUFFMAN_TABLE[index]

        if flags & HUFFMAN_FAIL:
            msg = "Invalid Huffman string"
            raise HPACKDecodingError(msg)

        if flags & HUFFMAN_EMIT_SYMBOL:
            decoded_bytes.append(output_byte)

        index = (state * 16) + (input_byte & 0x0F)
        state, flags, output_byte = HUFFMAN_TABLE[index]

        if flags & HUFFMAN_FAIL:
            msg = "Invalid Huffman string"
            raise HPACKDecodingError(msg)

        if flags & HUFFMAN_EMIT_SYMBOL:
            decoded_bytes.append(output_byte)

    if not (flags & HUFFMAN_COMPLETE):
        msg = "Incomplete Huffman string"
        raise HPACKDecodingError(msg)

    return bytes(decoded_bytes)


# Some decoder flags to control state transitions.
HUFFMAN_COMPLETE = 1
HUFFMAN_EMIT_SYMBOL = (1 << 1)
HUFFMAN_FAIL = (1 << 2)

# This is the monster table. Avert your eyes, children.
HUFFMAN_TABLE = [
    # Node 0 (Root Node, never emits symbols.)
    (4, 0, 0),
    (5, 0, 0),
    (7, 0, 0),
    (8, 0, 0),
    (11, 0, 0),
    (12, 0, 0),
    (16, 0, 0),
    (19, 0, 0),
    (25, 0, 0),
    (28, 0, 0),
    (32, 0, 0),
    (35, 0, 0),
    (42, 0, 0),
    (49, 0, 0),
    (57, 0, 0),
    (64, HUFFMAN_COMPLETE, 0),

    # Node 1
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 48),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 49),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 50),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 97),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 99),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 101),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 105),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 111),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 115),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 116),
    (13, 0, 0),
    (14, 0, 0),
    (17, 0, 0),
    (18, 0, 0),
    (20, 0, 0),
    (21, 0, 0),

    # Node 2
    (1, HUFFMAN_EMIT_SYMBOL, 48),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 48),
    (1, HUFFMAN_EMIT_SYMBOL, 49),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 49),
    (1, HUFFMAN_EMIT_SYMBOL, 50),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 50),
    (1, HUFFMAN_EMIT_SYMBOL, 97),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 97),
    (1, HUFFMAN_EMIT_SYMBOL, 99),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 99),
    (1, HUFFMAN_EMIT_SYMBOL, 101),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 101),
    (1, HUFFMAN_EMIT_SYMBOL, 105),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 105),
    (1, HUFFMAN_EMIT_SYMBOL, 111),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 111),

    # Node 3
    (2, HUFFMAN_EMIT_SYMBOL, 48),
    (9, HUFFMAN_EMIT_SYMBOL, 48),
    (23, HUFFMAN_EMIT_SYMBOL, 48),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 48),
    (2, HUFFMAN_EMIT_SYMBOL, 49),
    (9, HUFFMAN_EMIT_SYMBOL, 49),
    (23, HUFFMAN_EMIT_SYMBOL, 49),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 49),
    (2, HUFFMAN_EMIT_SYMBOL, 50),
    (9, HUFFMAN_EMIT_SYMBOL, 50),
    (23, HUFFMAN_EMIT_SYMBOL, 50),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 50),
    (2, HUFFMAN_EMIT_SYMBOL, 97),
    (9, HUFFMAN_EMIT_SYMBOL, 97),
    (23, HUFFMAN_EMIT_SYMBOL, 97),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 97),

    # Node 4
    (3, HUFFMAN_EMIT_SYMBOL, 48),
    (6, HUFFMAN_EMIT_SYMBOL, 48),
    (10, HUFFMAN_EMIT_SYMBOL, 48),
    (15, HUFFMAN_EMIT_SYMBOL, 48),
    (24, HUFFMAN_EMIT_SYMBOL, 48),
    (31, HUFFMAN_EMIT_SYMBOL, 48),
    (41, HUFFMAN_EMIT_SYMBOL, 48),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 48),
    (3, HUFFMAN_EMIT_SYMBOL, 49),
    (6, HUFFMAN_EMIT_SYMBOL, 49),
    (10, HUFFMAN_EMIT_SYMBOL, 49),
    (15, HUFFMAN_EMIT_SYMBOL, 49),
    (24, HUFFMAN_EMIT_SYMBOL, 49),
    (31, HUFFMAN_EMIT_SYMBOL, 49),
    (41, HUFFMAN_EMIT_SYMBOL, 49),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 49),

    # Node 5
    (3, HUFFMAN_EMIT_SYMBOL, 50),
    (6, HUFFMAN_EMIT_SYMBOL, 50),
    (10, HUFFMAN_EMIT_SYMBOL, 50),
    (15, HUFFMAN_EMIT_SYMBOL, 50),
    (24, HUFFMAN_EMIT_SYMBOL, 50),
    (31, HUFFMAN_EMIT_SYMBOL, 50),
    (41, HUFFMAN_EMIT_SYMBOL, 50),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 50),
    (3, HUFFMAN_EMIT_SYMBOL, 97),
    (6, HUFFMAN_EMIT_SYMBOL, 97),
    (10, HUFFMAN_EMIT_SYMBOL, 97),
    (15, HUFFMAN_EMIT_SYMBOL, 97),
    (24, HUFFMAN_EMIT_SYMBOL, 97),
    (31, HUFFMAN_EMIT_SYMBOL, 97),
    (41, HUFFMAN_EMIT_SYMBOL, 97),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 97),

    # Node 6
    (2, HUFFMAN_EMIT_SYMBOL, 99),
    (9, HUFFMAN_EMIT_SYMBOL, 99),
    (23, HUFFMAN_EMIT_SYMBOL, 99),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 99),
    (2, HUFFMAN_EMIT_SYMBOL, 101),
    (9, HUFFMAN_EMIT_SYMBOL, 101),
    (23, HUFFMAN_EMIT_SYMBOL, 101),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 101),
    (2, HUFFMAN_EMIT_SYMBOL, 105),
    (9, HUFFMAN_EMIT_SYMBOL, 105),
    (23, HUFFMAN_EMIT_SYMBOL, 105),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 105),
    (2, HUFFMAN_EMIT_SYMBOL, 111),
    (9, HUFFMAN_EMIT_SYMBOL, 111),
    (23, HUFFMAN_EMIT_SYMBOL, 111),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 111),

    # Node 7
    (3, HUFFMAN_EMIT_SYMBOL, 99),
    (6, HUFFMAN_EMIT_SYMBOL, 99),
    (10, HUFFMAN_EMIT_SYMBOL, 99),
    (15, HUFFMAN_EMIT_SYMBOL, 99),
    (24, HUFFMAN_EMIT_SYMBOL, 99),
    (31, HUFFMAN_EMIT_SYMBOL, 99),
    (41, HUFFMAN_EMIT_SYMBOL, 99),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 99),
    (3, HUFFMAN_EMIT_SYMBOL, 101),
    (6, HUFFMAN_EMIT_SYMBOL, 101),
    (10, HUFFMAN_EMIT_SYMBOL, 101),
    (15, HUFFMAN_EMIT_SYMBOL, 101),
    (24, HUFFMAN_EMIT_SYMBOL, 101),
    (31, HUFFMAN_EMIT_SYMBOL, 101),
    (41, HUFFMAN_EMIT_SYMBOL, 101),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 101),

    # Node 8
    (3, HUFFMAN_EMIT_SYMBOL, 105),
    (6, HUFFMAN_EMIT_SYMBOL, 105),
    (10, HUFFMAN_EMIT_SYMBOL, 105),
    (15, HUFFMAN_EMIT_SYMBOL, 105),
    (24, HUFFMAN_EMIT_SYMBOL, 105),
    (31, HUFFMAN_EMIT_SYMBOL, 105),
    (41, HUFFMAN_EMIT_SYMBOL, 105),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 105),
    (3, HUFFMAN_EMIT_SYMBOL, 111),
    (6, HUFFMAN_EMIT_SYMBOL, 111),
    (10, HUFFMAN_EMIT_SYMBOL, 111),
    (15, HUFFMAN_EMIT_SYMBOL, 111),
    (24, HUFFMAN_EMIT_SYMBOL, 111),
    (31, HUFFMAN_EMIT_SYMBOL, 111),
    (41, HUFFMAN_EMIT_SYMBOL, 111),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 111),

    # Node 9
    (1, HUFFMAN_EMIT_SYMBOL, 115),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 115),
    (1, HUFFMAN_EMIT_SYMBOL, 116),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 116),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 32),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 37),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 45),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 46),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 47),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 51),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 52),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 53),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 54),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 55),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 56),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 57),

    # Node 10
    (2, HUFFMAN_EMIT_SYMBOL, 115),
    (9, HUFFMAN_EMIT_SYMBOL, 115),
    (23, HUFFMAN_EMIT_SYMBOL, 115),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 115),
    (2, HUFFMAN_EMIT_SYMBOL, 116),
    (9, HUFFMAN_EMIT_SYMBOL, 116),
    (23, HUFFMAN_EMIT_SYMBOL, 116),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 116),
    (1, HUFFMAN_EMIT_SYMBOL, 32),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 32),
    (1, HUFFMAN_EMIT_SYMBOL, 37),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 37),
    (1, HUFFMAN_EMIT_SYMBOL, 45),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 45),
    (1, HUFFMAN_EMIT_SYMBOL, 46),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 46),

    # Node 11
    (3, HUFFMAN_EMIT_SYMBOL, 115),
    (6, HUFFMAN_EMIT_SYMBOL, 115),
    (10, HUFFMAN_EMIT_SYMBOL, 115),
    (15, HUFFMAN_EMIT_SYMBOL, 115),
    (24, HUFFMAN_EMIT_SYMBOL, 115),
    (31, HUFFMAN_EMIT_SYMBOL, 115),
    (41, HUFFMAN_EMIT_SYMBOL, 115),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 115),
    (3, HUFFMAN_EMIT_SYMBOL, 116),
    (6, HUFFMAN_EMIT_SYMBOL, 116),
    (10, HUFFMAN_EMIT_SYMBOL, 116),
    (15, HUFFMAN_EMIT_SYMBOL, 116),
    (24, HUFFMAN_EMIT_SYMBOL, 116),
    (31, HUFFMAN_EMIT_SYMBOL, 116),
    (41, HUFFMAN_EMIT_SYMBOL, 116),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 116),

    # Node 12
    (2, HUFFMAN_EMIT_SYMBOL, 32),
    (9, HUFFMAN_EMIT_SYMBOL, 32),
    (23, HUFFMAN_EMIT_SYMBOL, 32),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 32),
    (2, HUFFMAN_EMIT_SYMBOL, 37),
    (9, HUFFMAN_EMIT_SYMBOL, 37),
    (23, HUFFMAN_EMIT_SYMBOL, 37),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 37),
    (2, HUFFMAN_EMIT_SYMBOL, 45),
    (9, HUFFMAN_EMIT_SYMBOL, 45),
    (23, HUFFMAN_EMIT_SYMBOL, 45),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 45),
    (2, HUFFMAN_EMIT_SYMBOL, 46),
    (9, HUFFMAN_EMIT_SYMBOL, 46),
    (23, HUFFMAN_EMIT_SYMBOL, 46),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 46),

    # Node 13
    (3, HUFFMAN_EMIT_SYMBOL, 32),
    (6, HUFFMAN_EMIT_SYMBOL, 32),
    (10, HUFFMAN_EMIT_SYMBOL, 32),
    (15, HUFFMAN_EMIT_SYMBOL, 32),
    (24, HUFFMAN_EMIT_SYMBOL, 32),
    (31, HUFFMAN_EMIT_SYMBOL, 32),
    (41, HUFFMAN_EMIT_SYMBOL, 32),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 32),
    (3, HUFFMAN_EMIT_SYMBOL, 37),
    (6, HUFFMAN_EMIT_SYMBOL, 37),
    (10, HUFFMAN_EMIT_SYMBOL, 37),
    (15, HUFFMAN_EMIT_SYMBOL, 37),
    (24, HUFFMAN_EMIT_SYMBOL, 37),
    (31, HUFFMAN_EMIT_SYMBOL, 37),
    (41, HUFFMAN_EMIT_SYMBOL, 37),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 37),

    # Node 14
    (3, HUFFMAN_EMIT_SYMBOL, 45),
    (6, HUFFMAN_EMIT_SYMBOL, 45),
    (10, HUFFMAN_EMIT_SYMBOL, 45),
    (15, HUFFMAN_EMIT_SYMBOL, 45),
    (24, HUFFMAN_EMIT_SYMBOL, 45),
    (31, HUFFMAN_EMIT_SYMBOL, 45),
    (41, HUFFMAN_EMIT_SYMBOL, 45),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 45),
    (3, HUFFMAN_EMIT_SYMBOL, 46),
    (6, HUFFMAN_EMIT_SYMBOL, 46),
    (10, HUFFMAN_EMIT_SYMBOL, 46),
    (15, HUFFMAN_EMIT_SYMBOL, 46),
    (24, HUFFMAN_EMIT_SYMBOL, 46),
    (31, HUFFMAN_EMIT_SYMBOL, 46),
    (41, HUFFMAN_EMIT_SYMBOL, 46),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 46),

    # Node 15
    (1, HUFFMAN_EMIT_SYMBOL, 47),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 47),
    (1, HUFFMAN_EMIT_SYMBOL, 51),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 51),
    (1, HUFFMAN_EMIT_SYMBOL, 52),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 52),
    (1, HUFFMAN_EMIT_SYMBOL, 53),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 53),
    (1, HUFFMAN_EMIT_SYMBOL, 54),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 54),
    (1, HUFFMAN_EMIT_SYMBOL, 55),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 55),
    (1, HUFFMAN_EMIT_SYMBOL, 56),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 56),
    (1, HUFFMAN_EMIT_SYMBOL, 57),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 57),

    # Node 16
    (2, HUFFMAN_EMIT_SYMBOL, 47),
    (9, HUFFMAN_EMIT_SYMBOL, 47),
    (23, HUFFMAN_EMIT_SYMBOL, 47),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 47),
    (2, HUFFMAN_EMIT_SYMBOL, 51),
    (9, HUFFMAN_EMIT_SYMBOL, 51),
    (23, HUFFMAN_EMIT_SYMBOL, 51),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 51),
    (2, HUFFMAN_EMIT_SYMBOL, 52),
    (9, HUFFMAN_EMIT_SYMBOL, 52),
    (23, HUFFMAN_EMIT_SYMBOL, 52),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 52),
    (2, HUFFMAN_EMIT_SYMBOL, 53),
    (9, HUFFMAN_EMIT_SYMBOL, 53),
    (23, HUFFMAN_EMIT_SYMBOL, 53),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 53),

    # Node 17
    (3, HUFFMAN_EMIT_SYMBOL, 47),
    (6, HUFFMAN_EMIT_SYMBOL, 47),
    (10, HUFFMAN_EMIT_SYMBOL, 47),
    (15, HUFFMAN_EMIT_SYMBOL, 47),
    (24, HUFFMAN_EMIT_SYMBOL, 47),
    (31, HUFFMAN_EMIT_SYMBOL, 47),
    (41, HUFFMAN_EMIT_SYMBOL, 47),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 47),
    (3, HUFFMAN_EMIT_SYMBOL, 51),
    (6, HUFFMAN_EMIT_SYMBOL, 51),
    (10, HUFFMAN_EMIT_SYMBOL, 51),
    (15, HUFFMAN_EMIT_SYMBOL, 51),
    (24, HUFFMAN_EMIT_SYMBOL, 51),
    (31, HUFFMAN_EMIT_SYMBOL, 51),
    (41, HUFFMAN_EMIT_SYMBOL, 51),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 51),

    # Node 18
    (3, HUFFMAN_EMIT_SYMBOL, 52),
    (6, HUFFMAN_EMIT_SYMBOL, 52),
    (10, HUFFMAN_EMIT_SYMBOL, 52),
    (15, HUFFMAN_EMIT_SYMBOL, 52),
    (24, HUFFMAN_EMIT_SYMBOL, 52),
    (31, HUFFMAN_EMIT_SYMBOL, 52),
    (41, HUFFMAN_EMIT_SYMBOL, 52),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 52),
    (3, HUFFMAN_EMIT_SYMBOL, 53),
    (6, HUFFMAN_EMIT_SYMBOL, 53),
    (10, HUFFMAN_EMIT_SYMBOL, 53),
    (15, HUFFMAN_EMIT_SYMBOL, 53),
    (24, HUFFMAN_EMIT_SYMBOL, 53),
    (31, HUFFMAN_EMIT_SYMBOL, 53),
    (41, HUFFMAN_EMIT_SYMBOL, 53),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 53),

    # Node 19
    (2, HUFFMAN_EMIT_SYMBOL, 54),
    (9, HUFFMAN_EMIT_SYMBOL, 54),
    (23, HUFFMAN_EMIT_SYMBOL, 54),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 54),
    (2, HUFFMAN_EMIT_SYMBOL, 55),
    (9, HUFFMAN_EMIT_SYMBOL, 55),
    (23, HUFFMAN_EMIT_SYMBOL, 55),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 55),
    (2, HUFFMAN_EMIT_SYMBOL, 56),
    (9, HUFFMAN_EMIT_SYMBOL, 56),
    (23, HUFFMAN_EMIT_SYMBOL, 56),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 56),
    (2, HUFFMAN_EMIT_SYMBOL, 57),
    (9, HUFFMAN_EMIT_SYMBOL, 57),
    (23, HUFFMAN_EMIT_SYMBOL, 57),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 57),

    # Node 20
    (3, HUFFMAN_EMIT_SYMBOL, 54),
    (6, HUFFMAN_EMIT_SYMBOL, 54),
    (10, HUFFMAN_EMIT_SYMBOL, 54),
    (15, HUFFMAN_EMIT_SYMBOL, 54),
    (24, HUFFMAN_EMIT_SYMBOL, 54),
    (31, HUFFMAN_EMIT_SYMBOL, 54),
    (41, HUFFMAN_EMIT_SYMBOL, 54),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 54),
    (3, HUFFMAN_EMIT_SYMBOL, 55),
    (6, HUFFMAN_EMIT_SYMBOL, 55),
    (10, HUFFMAN_EMIT_SYMBOL, 55),
    (15, HUFFMAN_EMIT_SYMBOL, 55),
    (24, HUFFMAN_EMIT_SYMBOL, 55),
    (31, HUFFMAN_EMIT_SYMBOL, 55),
    (41, HUFFMAN_EMIT_SYMBOL, 55),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 55),

    # Node 21
    (3, HUFFMAN_EMIT_SYMBOL, 56),
    (6, HUFFMAN_EMIT_SYMBOL, 56),
    (10, HUFFMAN_EMIT_SYMBOL, 56),
    (15, HUFFMAN_EMIT_SYMBOL, 56),
    (24, HUFFMAN_EMIT_SYMBOL, 56),
    (31, HUFFMAN_EMIT_SYMBOL, 56),
    (41, HUFFMAN_EMIT_SYMBOL, 56),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 56),
    (3, HUFFMAN_EMIT_SYMBOL, 57),
    (6, HUFFMAN_EMIT_SYMBOL, 57),
    (10, HUFFMAN_EMIT_SYMBOL, 57),
    (15, HUFFMAN_EMIT_SYMBOL, 57),
    (24, HUFFMAN_EMIT_SYMBOL, 57),
    (31, HUFFMAN_EMIT_SYMBOL, 57),
    (41, HUFFMAN_EMIT_SYMBOL, 57),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 57),

    # Node 22
    (26, 0, 0),
    (27, 0, 0),
    (29, 0, 0),
    (30, 0, 0),
    (33, 0, 0),
    (34, 0, 0),
    (36, 0, 0),
    (37, 0, 0),
    (43, 0, 0),
    (46, 0, 0),
    (50, 0, 0),
    (53, 0, 0),
    (58, 0, 0),
    (61, 0, 0),
    (65, 0, 0),
    (68, HUFFMAN_COMPLETE, 0),

    # Node 23
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 61),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 65),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 95),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 98),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 100),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 102),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 103),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 104),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 108),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 109),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 110),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 112),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 114),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 117),
    (38, 0, 0),
    (39, 0, 0),

    # Node 24
    (1, HUFFMAN_EMIT_SYMBOL, 61),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 61),
    (1, HUFFMAN_EMIT_SYMBOL, 65),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 65),
    (1, HUFFMAN_EMIT_SYMBOL, 95),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 95),
    (1, HUFFMAN_EMIT_SYMBOL, 98),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 98),
    (1, HUFFMAN_EMIT_SYMBOL, 100),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 100),
    (1, HUFFMAN_EMIT_SYMBOL, 102),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 102),
    (1, HUFFMAN_EMIT_SYMBOL, 103),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 103),
    (1, HUFFMAN_EMIT_SYMBOL, 104),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 104),

    # Node 25
    (2, HUFFMAN_EMIT_SYMBOL, 61),
    (9, HUFFMAN_EMIT_SYMBOL, 61),
    (23, HUFFMAN_EMIT_SYMBOL, 61),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 61),
    (2, HUFFMAN_EMIT_SYMBOL, 65),
    (9, HUFFMAN_EMIT_SYMBOL, 65),
    (23, HUFFMAN_EMIT_SYMBOL, 65),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 65),
    (2, HUFFMAN_EMIT_SYMBOL, 95),
    (9, HUFFMAN_EMIT_SYMBOL, 95),
    (23, HUFFMAN_EMIT_SYMBOL, 95),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 95),
    (2, HUFFMAN_EMIT_SYMBOL, 98),
    (9, HUFFMAN_EMIT_SYMBOL, 98),
    (23, HUFFMAN_EMIT_SYMBOL, 98),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 98),

    # Node 26
    (3, HUFFMAN_EMIT_SYMBOL, 61),
    (6, HUFFMAN_EMIT_SYMBOL, 61),
    (10, HUFFMAN_EMIT_SYMBOL, 61),
    (15, HUFFMAN_EMIT_SYMBOL, 61),
    (24, HUFFMAN_EMIT_SYMBOL, 61),
    (31, HUFFMAN_EMIT_SYMBOL, 61),
    (41, HUFFMAN_EMIT_SYMBOL, 61),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 61),
    (3, HUFFMAN_EMIT_SYMBOL, 65),
    (6, HUFFMAN_EMIT_SYMBOL, 65),
    (10, HUFFMAN_EMIT_SYMBOL, 65),
    (15, HUFFMAN_EMIT_SYMBOL, 65),
    (24, HUFFMAN_EMIT_SYMBOL, 65),
    (31, HUFFMAN_EMIT_SYMBOL, 65),
    (41, HUFFMAN_EMIT_SYMBOL, 65),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 65),

    # Node 27
    (3, HUFFMAN_EMIT_SYMBOL, 95),
    (6, HUFFMAN_EMIT_SYMBOL, 95),
    (10, HUFFMAN_EMIT_SYMBOL, 95),
    (15, HUFFMAN_EMIT_SYMBOL, 95),
    (24, HUFFMAN_EMIT_SYMBOL, 95),
    (31, HUFFMAN_EMIT_SYMBOL, 95),
    (41, HUFFMAN_EMIT_SYMBOL, 95),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 95),
    (3, HUFFMAN_EMIT_SYMBOL, 98),
    (6, HUFFMAN_EMIT_SYMBOL, 98),
    (10, HUFFMAN_EMIT_SYMBOL, 98),
    (15, HUFFMAN_EMIT_SYMBOL, 98),
    (24, HUFFMAN_EMIT_SYMBOL, 98),
    (31, HUFFMAN_EMIT_SYMBOL, 98),
    (41, HUFFMAN_EMIT_SYMBOL, 98),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 98),

    # Node 28
    (2, HUFFMAN_EMIT_SYMBOL, 100),
    (9, HUFFMAN_EMIT_SYMBOL, 100),
    (23, HUFFMAN_EMIT_SYMBOL, 100),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 100),
    (2, HUFFMAN_EMIT_SYMBOL, 102),
    (9, HUFFMAN_EMIT_SYMBOL, 102),
    (23, HUFFMAN_EMIT_SYMBOL, 102),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 102),
    (2, HUFFMAN_EMIT_SYMBOL, 103),
    (9, HUFFMAN_EMIT_SYMBOL, 103),
    (23, HUFFMAN_EMIT_SYMBOL, 103),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 103),
    (2, HUFFMAN_EMIT_SYMBOL, 104),
    (9, HUFFMAN_EMIT_SYMBOL, 104),
    (23, HUFFMAN_EMIT_SYMBOL, 104),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 104),

    # Node 29
    (3, HUFFMAN_EMIT_SYMBOL, 100),
    (6, HUFFMAN_EMIT_SYMBOL, 100),
    (10, HUFFMAN_EMIT_SYMBOL, 100),
    (15, HUFFMAN_EMIT_SYMBOL, 100),
    (24, HUFFMAN_EMIT_SYMBOL, 100),
    (31, HUFFMAN_EMIT_SYMBOL, 100),
    (41, HUFFMAN_EMIT_SYMBOL, 100),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 100),
    (3, HUFFMAN_EMIT_SYMBOL, 102),
    (6, HUFFMAN_EMIT_SYMBOL, 102),
    (10, HUFFMAN_EMIT_SYMBOL, 102),
    (15, HUFFMAN_EMIT_SYMBOL, 102),
    (24, HUFFMAN_EMIT_SYMBOL, 102),
    (31, HUFFMAN_EMIT_SYMBOL, 102),
    (41, HUFFMAN_EMIT_SYMBOL, 102),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 102),

    # Node 30
    (3, HUFFMAN_EMIT_SYMBOL, 103),
    (6, HUFFMAN_EMIT_SYMBOL, 103),
    (10, HUFFMAN_EMIT_SYMBOL, 103),
    (15, HUFFMAN_EMIT_SYMBOL, 103),
    (24, HUFFMAN_EMIT_SYMBOL, 103),
    (31, HUFFMAN_EMIT_SYMBOL, 103),
    (41, HUFFMAN_EMIT_SYMBOL, 103),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 103),
    (3, HUFFMAN_EMIT_SYMBOL, 104),
    (6, HUFFMAN_EMIT_SYMBOL, 104),
    (10, HUFFMAN_EMIT_SYMBOL, 104),
    (15, HUFFMAN_EMIT_SYMBOL, 104),
    (24, HUFFMAN_EMIT_SYMBOL, 104),
    (31, HUFFMAN_EMIT_SYMBOL, 104),
    (41, HUFFMAN_EMIT_SYMBOL, 104),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 104),

    # Node 31
    (1, HUFFMAN_EMIT_SYMBOL, 108),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 108),
    (1, HUFFMAN_EMIT_SYMBOL, 109),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 109),
    (1, HUFFMAN_EMIT_SYMBOL, 110),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 110),
    (1, HUFFMAN_EMIT_SYMBOL, 112),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 112),
    (1, HUFFMAN_EMIT_SYMBOL, 114),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 114),
    (1, HUFFMAN_EMIT_SYMBOL, 117),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 117),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 58),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 66),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 67),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 68),

    # Node 32
    (2, HUFFMAN_EMIT_SYMBOL, 108),
    (9, HUFFMAN_EMIT_SYMBOL, 108),
    (23, HUFFMAN_EMIT_SYMBOL, 108),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 108),
    (2, HUFFMAN_EMIT_SYMBOL, 109),
    (9, HUFFMAN_EMIT_SYMBOL, 109),
    (23, HUFFMAN_EMIT_SYMBOL, 109),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 109),
    (2, HUFFMAN_EMIT_SYMBOL, 110),
    (9, HUFFMAN_EMIT_SYMBOL, 110),
    (23, HUFFMAN_EMIT_SYMBOL, 110),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 110),
    (2, HUFFMAN_EMIT_SYMBOL, 112),
    (9, HUFFMAN_EMIT_SYMBOL, 112),
    (23, HUFFMAN_EMIT_SYMBOL, 112),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 112),

    # Node 33
    (3, HUFFMAN_EMIT_SYMBOL, 108),
    (6, HUFFMAN_EMIT_SYMBOL, 108),
    (10, HUFFMAN_EMIT_SYMBOL, 108),
    (15, HUFFMAN_EMIT_SYMBOL, 108),
    (24, HUFFMAN_EMIT_SYMBOL, 108),
    (31, HUFFMAN_EMIT_SYMBOL, 108),
    (41, HUFFMAN_EMIT_SYMBOL, 108),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 108),
    (3, HUFFMAN_EMIT_SYMBOL, 109),
    (6, HUFFMAN_EMIT_SYMBOL, 109),
    (10, HUFFMAN_EMIT_SYMBOL, 109),
    (15, HUFFMAN_EMIT_SYMBOL, 109),
    (24, HUFFMAN_EMIT_SYMBOL, 109),
    (31, HUFFMAN_EMIT_SYMBOL, 109),
    (41, HUFFMAN_EMIT_SYMBOL, 109),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 109),

    # Node 34
    (3, HUFFMAN_EMIT_SYMBOL, 110),
    (6, HUFFMAN_EMIT_SYMBOL, 110),
    (10, HUFFMAN_EMIT_SYMBOL, 110),
    (15, HUFFMAN_EMIT_SYMBOL, 110),
    (24, HUFFMAN_EMIT_SYMBOL, 110),
    (31, HUFFMAN_EMIT_SYMBOL, 110),
    (41, HUFFMAN_EMIT_SYMBOL, 110),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 110),
    (3, HUFFMAN_EMIT_SYMBOL, 112),
    (6, HUFFMAN_EMIT_SYMBOL, 112),
    (10, HUFFMAN_EMIT_SYMBOL, 112),
    (15, HUFFMAN_EMIT_SYMBOL, 112),
    (24, HUFFMAN_EMIT_SYMBOL, 112),
    (31, HUFFMAN_EMIT_SYMBOL, 112),
    (41, HUFFMAN_EMIT_SYMBOL, 112),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 112),

    # Node 35
    (2, HUFFMAN_EMIT_SYMBOL, 114),
    (9, HUFFMAN_EMIT_SYMBOL, 114),
    (23, HUFFMAN_EMIT_SYMBOL, 114),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 114),
    (2, HUFFMAN_EMIT_SYMBOL, 117),
    (9, HUFFMAN_EMIT_SYMBOL, 117),
    (23, HUFFMAN_EMIT_SYMBOL, 117),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 117),
    (1, HUFFMAN_EMIT_SYMBOL, 58),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 58),
    (1, HUFFMAN_EMIT_SYMBOL, 66),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 66),
    (1, HUFFMAN_EMIT_SYMBOL, 67),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 67),
    (1, HUFFMAN_EMIT_SYMBOL, 68),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 68),

    # Node 36
    (3, HUFFMAN_EMIT_SYMBOL, 114),
    (6, HUFFMAN_EMIT_SYMBOL, 114),
    (10, HUFFMAN_EMIT_SYMBOL, 114),
    (15, HUFFMAN_EMIT_SYMBOL, 114),
    (24, HUFFMAN_EMIT_SYMBOL, 114),
    (31, HUFFMAN_EMIT_SYMBOL, 114),
    (41, HUFFMAN_EMIT_SYMBOL, 114),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 114),
    (3, HUFFMAN_EMIT_SYMBOL, 117),
    (6, HUFFMAN_EMIT_SYMBOL, 117),
    (10, HUFFMAN_EMIT_SYMBOL, 117),
    (15, HUFFMAN_EMIT_SYMBOL, 117),
    (24, HUFFMAN_EMIT_SYMBOL, 117),
    (31, HUFFMAN_EMIT_SYMBOL, 117),
    (41, HUFFMAN_EMIT_SYMBOL, 117),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 117),

    # Node 37
    (2, HUFFMAN_EMIT_SYMBOL, 58),
    (9, HUFFMAN_EMIT_SYMBOL, 58),
    (23, HUFFMAN_EMIT_SYMBOL, 58),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 58),
    (2, HUFFMAN_EMIT_SYMBOL, 66),
    (9, HUFFMAN_EMIT_SYMBOL, 66),
    (23, HUFFMAN_EMIT_SYMBOL, 66),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 66),
    (2, HUFFMAN_EMIT_SYMBOL, 67),
    (9, HUFFMAN_EMIT_SYMBOL, 67),
    (23, HUFFMAN_EMIT_SYMBOL, 67),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 67),
    (2, HUFFMAN_EMIT_SYMBOL, 68),
    (9, HUFFMAN_EMIT_SYMBOL, 68),
    (23, HUFFMAN_EMIT_SYMBOL, 68),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 68),

    # Node 38
    (3, HUFFMAN_EMIT_SYMBOL, 58),
    (6, HUFFMAN_EMIT_SYMBOL, 58),
    (10, HUFFMAN_EMIT_SYMBOL, 58),
    (15, HUFFMAN_EMIT_SYMBOL, 58),
    (24, HUFFMAN_EMIT_SYMBOL, 58),
    (31, HUFFMAN_EMIT_SYMBOL, 58),
    (41, HUFFMAN_EMIT_SYMBOL, 58),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 58),
    (3, HUFFMAN_EMIT_SYMBOL, 66),
    (6, HUFFMAN_EMIT_SYMBOL, 66),
    (10, HUFFMAN_EMIT_SYMBOL, 66),
    (15, HUFFMAN_EMIT_SYMBOL, 66),
    (24, HUFFMAN_EMIT_SYMBOL, 66),
    (31, HUFFMAN_EMIT_SYMBOL, 66),
    (41, HUFFMAN_EMIT_SYMBOL, 66),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 66),

    # Node 39
    (3, HUFFMAN_EMIT_SYMBOL, 67),
    (6, HUFFMAN_EMIT_SYMBOL, 67),
    (10, HUFFMAN_EMIT_SYMBOL, 67),
    (15, HUFFMAN_EMIT_SYMBOL, 67),
    (24, HUFFMAN_EMIT_SYMBOL, 67),
    (31, HUFFMAN_EMIT_SYMBOL, 67),
    (41, HUFFMAN_EMIT_SYMBOL, 67),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 67),
    (3, HUFFMAN_EMIT_SYMBOL, 68),
    (6, HUFFMAN_EMIT_SYMBOL, 68),
    (10, HUFFMAN_EMIT_SYMBOL, 68),
    (15, HUFFMAN_EMIT_SYMBOL, 68),
    (24, HUFFMAN_EMIT_SYMBOL, 68),
    (31, HUFFMAN_EMIT_SYMBOL, 68),
    (41, HUFFMAN_EMIT_SYMBOL, 68),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 68),

    # Node 40
    (44, 0, 0),
    (45, 0, 0),
    (47, 0, 0),
    (48, 0, 0),
    (51, 0, 0),
    (52, 0, 0),
    (54, 0, 0),
    (55, 0, 0),
    (59, 0, 0),
    (60, 0, 0),
    (62, 0, 0),
    (63, 0, 0),
    (66, 0, 0),
    (67, 0, 0),
    (69, 0, 0),
    (72, HUFFMAN_COMPLETE, 0),

    # Node 41
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 69),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 70),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 71),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 72),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 73),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 74),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 75),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 76),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 77),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 78),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 79),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 80),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 81),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 82),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 83),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 84),

    # Node 42
    (1, HUFFMAN_EMIT_SYMBOL, 69),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 69),
    (1, HUFFMAN_EMIT_SYMBOL, 70),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 70),
    (1, HUFFMAN_EMIT_SYMBOL, 71),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 71),
    (1, HUFFMAN_EMIT_SYMBOL, 72),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 72),
    (1, HUFFMAN_EMIT_SYMBOL, 73),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 73),
    (1, HUFFMAN_EMIT_SYMBOL, 74),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 74),
    (1, HUFFMAN_EMIT_SYMBOL, 75),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 75),
    (1, HUFFMAN_EMIT_SYMBOL, 76),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 76),

    # Node 43
    (2, HUFFMAN_EMIT_SYMBOL, 69),
    (9, HUFFMAN_EMIT_SYMBOL, 69),
    (23, HUFFMAN_EMIT_SYMBOL, 69),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 69),
    (2, HUFFMAN_EMIT_SYMBOL, 70),
    (9, HUFFMAN_EMIT_SYMBOL, 70),
    (23, HUFFMAN_EMIT_SYMBOL, 70),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 70),
    (2, HUFFMAN_EMIT_SYMBOL, 71),
    (9, HUFFMAN_EMIT_SYMBOL, 71),
    (23, HUFFMAN_EMIT_SYMBOL, 71),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 71),
    (2, HUFFMAN_EMIT_SYMBOL, 72),
    (9, HUFFMAN_EMIT_SYMBOL, 72),
    (23, HUFFMAN_EMIT_SYMBOL, 72),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 72),

    # Node 44
    (3, HUFFMAN_EMIT_SYMBOL, 69),
    (6, HUFFMAN_EMIT_SYMBOL, 69),
    (10, HUFFMAN_EMIT_SYMBOL, 69),
    (15, HUFFMAN_EMIT_SYMBOL, 69),
    (24, HUFFMAN_EMIT_SYMBOL, 69),
    (31, HUFFMAN_EMIT_SYMBOL, 69),
    (41, HUFFMAN_EMIT_SYMBOL, 69),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 69),
    (3, HUFFMAN_EMIT_SYMBOL, 70),
    (6, HUFFMAN_EMIT_SYMBOL, 70),
    (10, HUFFMAN_EMIT_SYMBOL, 70),
    (15, HUFFMAN_EMIT_SYMBOL, 70),
    (24, HUFFMAN_EMIT_SYMBOL, 70),
    (31, HUFFMAN_EMIT_SYMBOL, 70),
    (41, HUFFMAN_EMIT_SYMBOL, 70),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 70),

    # Node 45
    (3, HUFFMAN_EMIT_SYMBOL, 71),
    (6, HUFFMAN_EMIT_SYMBOL, 71),
    (10, HUFFMAN_EMIT_SYMBOL, 71),
    (15, HUFFMAN_EMIT_SYMBOL, 71),
    (24, HUFFMAN_EMIT_SYMBOL, 71),
    (31, HUFFMAN_EMIT_SYMBOL, 71),
    (41, HUFFMAN_EMIT_SYMBOL, 71),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 71),
    (3, HUFFMAN_EMIT_SYMBOL, 72),
    (6, HUFFMAN_EMIT_SYMBOL, 72),
    (10, HUFFMAN_EMIT_SYMBOL, 72),
    (15, HUFFMAN_EMIT_SYMBOL, 72),
    (24, HUFFMAN_EMIT_SYMBOL, 72),
    (31, HUFFMAN_EMIT_SYMBOL, 72),
    (41, HUFFMAN_EMIT_SYMBOL, 72),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 72),

    # Node 46
    (2, HUFFMAN_EMIT_SYMBOL, 73),
    (9, HUFFMAN_EMIT_SYMBOL, 73),
    (23, HUFFMAN_EMIT_SYMBOL, 73),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 73),
    (2, HUFFMAN_EMIT_SYMBOL, 74),
    (9, HUFFMAN_EMIT_SYMBOL, 74),
    (23, HUFFMAN_EMIT_SYMBOL, 74),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 74),
    (2, HUFFMAN_EMIT_SYMBOL, 75),
    (9, HUFFMAN_EMIT_SYMBOL, 75),
    (23, HUFFMAN_EMIT_SYMBOL, 75),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 75),
    (2, HUFFMAN_EMIT_SYMBOL, 76),
    (9, HUFFMAN_EMIT_SYMBOL, 76),
    (23, HUFFMAN_EMIT_SYMBOL, 76),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 76),

    # Node 47
    (3, HUFFMAN_EMIT_SYMBOL, 73),
    (6, HUFFMAN_EMIT_SYMBOL, 73),
    (10, HUFFMAN_EMIT_SYMBOL, 73),
    (15, HUFFMAN_EMIT_SYMBOL, 73),
    (24, HUFFMAN_EMIT_SYMBOL, 73),
    (31, HUFFMAN_EMIT_SYMBOL, 73),
    (41, HUFFMAN_EMIT_SYMBOL, 73),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 73),
    (3, HUFFMAN_EMIT_SYMBOL, 74),
    (6, HUFFMAN_EMIT_SYMBOL, 74),
    (10, HUFFMAN_EMIT_SYMBOL, 74),
    (15, HUFFMAN_EMIT_SYMBOL, 74),
    (24, HUFFMAN_EMIT_SYMBOL, 74),
    (31, HUFFMAN_EMIT_SYMBOL, 74),
    (41, HUFFMAN_EMIT_SYMBOL, 74),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 74),

    # Node 48
    (3, HUFFMAN_EMIT_SYMBOL, 75),
    (6, HUFFMAN_EMIT_SYMBOL, 75),
    (10, HUFFMAN_EMIT_SYMBOL, 75),
    (15, HUFFMAN_EMIT_SYMBOL, 75),
    (24, HUFFMAN_EMIT_SYMBOL, 75),
    (31, HUFFMAN_EMIT_SYMBOL, 75),
    (41, HUFFMAN_EMIT_SYMBOL, 75),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 75),
    (3, HUFFMAN_EMIT_SYMBOL, 76),
    (6, HUFFMAN_EMIT_SYMBOL, 76),
    (10, HUFFMAN_EMIT_SYMBOL, 76),
    (15, HUFFMAN_EMIT_SYMBOL, 76),
    (24, HUFFMAN_EMIT_SYMBOL, 76),
    (31, HUFFMAN_EMIT_SYMBOL, 76),
    (41, HUFFMAN_EMIT_SYMBOL, 76),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 76),

    # Node 49
    (1, HUFFMAN_EMIT_SYMBOL, 77),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 77),
    (1, HUFFMAN_EMIT_SYMBOL, 78),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 78),
    (1, HUFFMAN_EMIT_SYMBOL, 79),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 79),
    (1, HUFFMAN_EMIT_SYMBOL, 80),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 80),
    (1, HUFFMAN_EMIT_SYMBOL, 81),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 81),
    (1, HUFFMAN_EMIT_SYMBOL, 82),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 82),
    (1, HUFFMAN_EMIT_SYMBOL, 83),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 83),
    (1, HUFFMAN_EMIT_SYMBOL, 84),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 84),

    # Node 50
    (2, HUFFMAN_EMIT_SYMBOL, 77),
    (9, HUFFMAN_EMIT_SYMBOL, 77),
    (23, HUFFMAN_EMIT_SYMBOL, 77),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 77),
    (2, HUFFMAN_EMIT_SYMBOL, 78),
    (9, HUFFMAN_EMIT_SYMBOL, 78),
    (23, HUFFMAN_EMIT_SYMBOL, 78),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 78),
    (2, HUFFMAN_EMIT_SYMBOL, 79),
    (9, HUFFMAN_EMIT_SYMBOL, 79),
    (23, HUFFMAN_EMIT_SYMBOL, 79),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 79),
    (2, HUFFMAN_EMIT_SYMBOL, 80),
    (9, HUFFMAN_EMIT_SYMBOL, 80),
    (23, HUFFMAN_EMIT_SYMBOL, 80),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 80),

    # Node 51
    (3, HUFFMAN_EMIT_SYMBOL, 77),
    (6, HUFFMAN_EMIT_SYMBOL, 77),
    (10, HUFFMAN_EMIT_SYMBOL, 77),
    (15, HUFFMAN_EMIT_SYMBOL, 77),
    (24, HUFFMAN_EMIT_SYMBOL, 77),
    (31, HUFFMAN_EMIT_SYMBOL, 77),
    (41, HUFFMAN_EMIT_SYMBOL, 77),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 77),
    (3, HUFFMAN_EMIT_SYMBOL, 78),
    (6, HUFFMAN_EMIT_SYMBOL, 78),
    (10, HUFFMAN_EMIT_SYMBOL, 78),
    (15, HUFFMAN_EMIT_SYMBOL, 78),
    (24, HUFFMAN_EMIT_SYMBOL, 78),
    (31, HUFFMAN_EMIT_SYMBOL, 78),
    (41, HUFFMAN_EMIT_SYMBOL, 78),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 78),

    # Node 52
    (3, HUFFMAN_EMIT_SYMBOL, 79),
    (6, HUFFMAN_EMIT_SYMBOL, 79),
    (10, HUFFMAN_EMIT_SYMBOL, 79),
    (15, HUFFMAN_EMIT_SYMBOL, 79),
    (24, HUFFMAN_EMIT_SYMBOL, 79),
    (31, HUFFMAN_EMIT_SYMBOL, 79),
    (41, HUFFMAN_EMIT_SYMBOL, 79),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 79),
    (3, HUFFMAN_EMIT_SYMBOL, 80),
    (6, HUFFMAN_EMIT_SYMBOL, 80),
    (10, HUFFMAN_EMIT_SYMBOL, 80),
    (15, HUFFMAN_EMIT_SYMBOL, 80),
    (24, HUFFMAN_EMIT_SYMBOL, 80),
    (31, HUFFMAN_EMIT_SYMBOL, 80),
    (41, HUFFMAN_EMIT_SYMBOL, 80),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 80),

    # Node 53
    (2, HUFFMAN_EMIT_SYMBOL, 81),
    (9, HUFFMAN_EMIT_SYMBOL, 81),
    (23, HUFFMAN_EMIT_SYMBOL, 81),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 81),
    (2, HUFFMAN_EMIT_SYMBOL, 82),
    (9, HUFFMAN_EMIT_SYMBOL, 82),
    (23, HUFFMAN_EMIT_SYMBOL, 82),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 82),
    (2, HUFFMAN_EMIT_SYMBOL, 83),
    (9, HUFFMAN_EMIT_SYMBOL, 83),
    (23, HUFFMAN_EMIT_SYMBOL, 83),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 83),
    (2, HUFFMAN_EMIT_SYMBOL, 84),
    (9, HUFFMAN_EMIT_SYMBOL, 84),
    (23, HUFFMAN_EMIT_SYMBOL, 84),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 84),

    # Node 54
    (3, HUFFMAN_EMIT_SYMBOL, 81),
    (6, HUFFMAN_EMIT_SYMBOL, 81),
    (10, HUFFMAN_EMIT_SYMBOL, 81),
    (15, HUFFMAN_EMIT_SYMBOL, 81),
    (24, HUFFMAN_EMIT_SYMBOL, 81),
    (31, HUFFMAN_EMIT_SYMBOL, 81),
    (41, HUFFMAN_EMIT_SYMBOL, 81),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 81),
    (3, HUFFMAN_EMIT_SYMBOL, 82),
    (6, HUFFMAN_EMIT_SYMBOL, 82),
    (10, HUFFMAN_EMIT_SYMBOL, 82),
    (15, HUFFMAN_EMIT_SYMBOL, 82),
    (24, HUFFMAN_EMIT_SYMBOL, 82),
    (31, HUFFMAN_EMIT_SYMBOL, 82),
    (41, HUFFMAN_EMIT_SYMBOL, 82),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 82),

    # Node 55
    (3, HUFFMAN_EMIT_SYMBOL, 83),
    (6, HUFFMAN_EMIT_SYMBOL, 83),
    (10, HUFFMAN_EMIT_SYMBOL, 83),
    (15, HUFFMAN_EMIT_SYMBOL, 83),
    (24, HUFFMAN_EMIT_SYMBOL, 83),
    (31, HUFFMAN_EMIT_SYMBOL, 83),
    (41, HUFFMAN_EMIT_SYMBOL, 83),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 83),
    (3, HUFFMAN_EMIT_SYMBOL, 84),
    (6, HUFFMAN_EMIT_SYMBOL, 84),
    (10, HUFFMAN_EMIT_SYMBOL, 84),
    (15, HUFFMAN_EMIT_SYMBOL, 84),
    (24, HUFFMAN_EMIT_SYMBOL, 84),
    (31, HUFFMAN_EMIT_SYMBOL, 84),
    (41, HUFFMAN_EMIT_SYMBOL, 84),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 84),

    # Node 56
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 85),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 86),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 87),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 89),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 106),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 107),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 113),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 118),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 119),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 120),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 121),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 122),
    (70, 0, 0),
    (71, 0, 0),
    (73, 0, 0),
    (74, HUFFMAN_COMPLETE, 0),

    # Node 57
    (1, HUFFMAN_EMIT_SYMBOL, 85),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 85),
    (1, HUFFMAN_EMIT_SYMBOL, 86),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 86),
    (1, HUFFMAN_EMIT_SYMBOL, 87),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 87),
    (1, HUFFMAN_EMIT_SYMBOL, 89),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 89),
    (1, HUFFMAN_EMIT_SYMBOL, 106),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 106),
    (1, HUFFMAN_EMIT_SYMBOL, 107),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 107),
    (1, HUFFMAN_EMIT_SYMBOL, 113),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 113),
    (1, HUFFMAN_EMIT_SYMBOL, 118),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 118),

    # Node 58
    (2, HUFFMAN_EMIT_SYMBOL, 85),
    (9, HUFFMAN_EMIT_SYMBOL, 85),
    (23, HUFFMAN_EMIT_SYMBOL, 85),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 85),
    (2, HUFFMAN_EMIT_SYMBOL, 86),
    (9, HUFFMAN_EMIT_SYMBOL, 86),
    (23, HUFFMAN_EMIT_SYMBOL, 86),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 86),
    (2, HUFFMAN_EMIT_SYMBOL, 87),
    (9, HUFFMAN_EMIT_SYMBOL, 87),
    (23, HUFFMAN_EMIT_SYMBOL, 87),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 87),
    (2, HUFFMAN_EMIT_SYMBOL, 89),
    (9, HUFFMAN_EMIT_SYMBOL, 89),
    (23, HUFFMAN_EMIT_SYMBOL, 89),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 89),

    # Node 59
    (3, HUFFMAN_EMIT_SYMBOL, 85),
    (6, HUFFMAN_EMIT_SYMBOL, 85),
    (10, HUFFMAN_EMIT_SYMBOL, 85),
    (15, HUFFMAN_EMIT_SYMBOL, 85),
    (24, HUFFMAN_EMIT_SYMBOL, 85),
    (31, HUFFMAN_EMIT_SYMBOL, 85),
    (41, HUFFMAN_EMIT_SYMBOL, 85),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 85),
    (3, HUFFMAN_EMIT_SYMBOL, 86),
    (6, HUFFMAN_EMIT_SYMBOL, 86),
    (10, HUFFMAN_EMIT_SYMBOL, 86),
    (15, HUFFMAN_EMIT_SYMBOL, 86),
    (24, HUFFMAN_EMIT_SYMBOL, 86),
    (31, HUFFMAN_EMIT_SYMBOL, 86),
    (41, HUFFMAN_EMIT_SYMBOL, 86),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 86),

    # Node 60
    (3, HUFFMAN_EMIT_SYMBOL, 87),
    (6, HUFFMAN_EMIT_SYMBOL, 87),
    (10, HUFFMAN_EMIT_SYMBOL, 87),
    (15, HUFFMAN_EMIT_SYMBOL, 87),
    (24, HUFFMAN_EMIT_SYMBOL, 87),
    (31, HUFFMAN_EMIT_SYMBOL, 87),
    (41, HUFFMAN_EMIT_SYMBOL, 87),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 87),
    (3, HUFFMAN_EMIT_SYMBOL, 89),
    (6, HUFFMAN_EMIT_SYMBOL, 89),
    (10, HUFFMAN_EMIT_SYMBOL, 89),
    (15, HUFFMAN_EMIT_SYMBOL, 89),
    (24, HUFFMAN_EMIT_SYMBOL, 89),
    (31, HUFFMAN_EMIT_SYMBOL, 89),
    (41, HUFFMAN_EMIT_SYMBOL, 89),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 89),

    # Node 61
    (2, HUFFMAN_EMIT_SYMBOL, 106),
    (9, HUFFMAN_EMIT_SYMBOL, 106),
    (23, HUFFMAN_EMIT_SYMBOL, 106),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 106),
    (2, HUFFMAN_EMIT_SYMBOL, 107),
    (9, HUFFMAN_EMIT_SYMBOL, 107),
    (23, HUFFMAN_EMIT_SYMBOL, 107),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 107),
    (2, HUFFMAN_EMIT_SYMBOL, 113),
    (9, HUFFMAN_EMIT_SYMBOL, 113),
    (23, HUFFMAN_EMIT_SYMBOL, 113),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 113),
    (2, HUFFMAN_EMIT_SYMBOL, 118),
    (9, HUFFMAN_EMIT_SYMBOL, 118),
    (23, HUFFMAN_EMIT_SYMBOL, 118),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 118),

    # Node 62
    (3, HUFFMAN_EMIT_SYMBOL, 106),
    (6, HUFFMAN_EMIT_SYMBOL, 106),
    (10, HUFFMAN_EMIT_SYMBOL, 106),
    (15, HUFFMAN_EMIT_SYMBOL, 106),
    (24, HUFFMAN_EMIT_SYMBOL, 106),
    (31, HUFFMAN_EMIT_SYMBOL, 106),
    (41, HUFFMAN_EMIT_SYMBOL, 106),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 106),
    (3, HUFFMAN_EMIT_SYMBOL, 107),
    (6, HUFFMAN_EMIT_SYMBOL, 107),
    (10, HUFFMAN_EMIT_SYMBOL, 107),
    (15, HUFFMAN_EMIT_SYMBOL, 107),
    (24, HUFFMAN_EMIT_SYMBOL, 107),
    (31, HUFFMAN_EMIT_SYMBOL, 107),
    (41, HUFFMAN_EMIT_SYMBOL, 107),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 107),

    # Node 63
    (3, HUFFMAN_EMIT_SYMBOL, 113),
    (6, HUFFMAN_EMIT_SYMBOL, 113),
    (10, HUFFMAN_EMIT_SYMBOL, 113),
    (15, HUFFMAN_EMIT_SYMBOL, 113),
    (24, HUFFMAN_EMIT_SYMBOL, 113),
    (31, HUFFMAN_EMIT_SYMBOL, 113),
    (41, HUFFMAN_EMIT_SYMBOL, 113),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 113),
    (3, HUFFMAN_EMIT_SYMBOL, 118),
    (6, HUFFMAN_EMIT_SYMBOL, 118),
    (10, HUFFMAN_EMIT_SYMBOL, 118),
    (15, HUFFMAN_EMIT_SYMBOL, 118),
    (24, HUFFMAN_EMIT_SYMBOL, 118),
    (31, HUFFMAN_EMIT_SYMBOL, 118),
    (41, HUFFMAN_EMIT_SYMBOL, 118),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 118),

    # Node 64
    (1, HUFFMAN_EMIT_SYMBOL, 119),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 119),
    (1, HUFFMAN_EMIT_SYMBOL, 120),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 120),
    (1, HUFFMAN_EMIT_SYMBOL, 121),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 121),
    (1, HUFFMAN_EMIT_SYMBOL, 122),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 122),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 38),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 42),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 44),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 59),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 88),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 90),
    (75, 0, 0),
    (78, 0, 0),

    # Node 65
    (2, HUFFMAN_EMIT_SYMBOL, 119),
    (9, HUFFMAN_EMIT_SYMBOL, 119),
    (23, HUFFMAN_EMIT_SYMBOL, 119),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 119),
    (2, HUFFMAN_EMIT_SYMBOL, 120),
    (9, HUFFMAN_EMIT_SYMBOL, 120),
    (23, HUFFMAN_EMIT_SYMBOL, 120),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 120),
    (2, HUFFMAN_EMIT_SYMBOL, 121),
    (9, HUFFMAN_EMIT_SYMBOL, 121),
    (23, HUFFMAN_EMIT_SYMBOL, 121),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 121),
    (2, HUFFMAN_EMIT_SYMBOL, 122),
    (9, HUFFMAN_EMIT_SYMBOL, 122),
    (23, HUFFMAN_EMIT_SYMBOL, 122),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 122),

    # Node 66
    (3, HUFFMAN_EMIT_SYMBOL, 119),
    (6, HUFFMAN_EMIT_SYMBOL, 119),
    (10, HUFFMAN_EMIT_SYMBOL, 119),
    (15, HUFFMAN_EMIT_SYMBOL, 119),
    (24, HUFFMAN_EMIT_SYMBOL, 119),
    (31, HUFFMAN_EMIT_SYMBOL, 119),
    (41, HUFFMAN_EMIT_SYMBOL, 119),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 119),
    (3, HUFFMAN_EMIT_SYMBOL, 120),
    (6, HUFFMAN_EMIT_SYMBOL, 120),
    (10, HUFFMAN_EMIT_SYMBOL, 120),
    (15, HUFFMAN_EMIT_SYMBOL, 120),
    (24, HUFFMAN_EMIT_SYMBOL, 120),
    (31, HUFFMAN_EMIT_SYMBOL, 120),
    (41, HUFFMAN_EMIT_SYMBOL, 120),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 120),

    # Node 67
    (3, HUFFMAN_EMIT_SYMBOL, 121),
    (6, HUFFMAN_EMIT_SYMBOL, 121),
    (10, HUFFMAN_EMIT_SYMBOL, 121),
    (15, HUFFMAN_EMIT_SYMBOL, 121),
    (24, HUFFMAN_EMIT_SYMBOL, 121),
    (31, HUFFMAN_EMIT_SYMBOL, 121),
    (41, HUFFMAN_EMIT_SYMBOL, 121),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 121),
    (3, HUFFMAN_EMIT_SYMBOL, 122),
    (6, HUFFMAN_EMIT_SYMBOL, 122),
    (10, HUFFMAN_EMIT_SYMBOL, 122),
    (15, HUFFMAN_EMIT_SYMBOL, 122),
    (24, HUFFMAN_EMIT_SYMBOL, 122),
    (31, HUFFMAN_EMIT_SYMBOL, 122),
    (41, HUFFMAN_EMIT_SYMBOL, 122),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 122),

    # Node 68
    (1, HUFFMAN_EMIT_SYMBOL, 38),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 38),
    (1, HUFFMAN_EMIT_SYMBOL, 42),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 42),
    (1, HUFFMAN_EMIT_SYMBOL, 44),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 44),
    (1, HUFFMAN_EMIT_SYMBOL, 59),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 59),
    (1, HUFFMAN_EMIT_SYMBOL, 88),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 88),
    (1, HUFFMAN_EMIT_SYMBOL, 90),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 90),
    (76, 0, 0),
    (77, 0, 0),
    (79, 0, 0),
    (81, 0, 0),

    # Node 69
    (2, HUFFMAN_EMIT_SYMBOL, 38),
    (9, HUFFMAN_EMIT_SYMBOL, 38),
    (23, HUFFMAN_EMIT_SYMBOL, 38),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 38),
    (2, HUFFMAN_EMIT_SYMBOL, 42),
    (9, HUFFMAN_EMIT_SYMBOL, 42),
    (23, HUFFMAN_EMIT_SYMBOL, 42),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 42),
    (2, HUFFMAN_EMIT_SYMBOL, 44),
    (9, HUFFMAN_EMIT_SYMBOL, 44),
    (23, HUFFMAN_EMIT_SYMBOL, 44),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 44),
    (2, HUFFMAN_EMIT_SYMBOL, 59),
    (9, HUFFMAN_EMIT_SYMBOL, 59),
    (23, HUFFMAN_EMIT_SYMBOL, 59),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 59),

    # Node 70
    (3, HUFFMAN_EMIT_SYMBOL, 38),
    (6, HUFFMAN_EMIT_SYMBOL, 38),
    (10, HUFFMAN_EMIT_SYMBOL, 38),
    (15, HUFFMAN_EMIT_SYMBOL, 38),
    (24, HUFFMAN_EMIT_SYMBOL, 38),
    (31, HUFFMAN_EMIT_SYMBOL, 38),
    (41, HUFFMAN_EMIT_SYMBOL, 38),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 38),
    (3, HUFFMAN_EMIT_SYMBOL, 42),
    (6, HUFFMAN_EMIT_SYMBOL, 42),
    (10, HUFFMAN_EMIT_SYMBOL, 42),
    (15, HUFFMAN_EMIT_SYMBOL, 42),
    (24, HUFFMAN_EMIT_SYMBOL, 42),
    (31, HUFFMAN_EMIT_SYMBOL, 42),
    (41, HUFFMAN_EMIT_SYMBOL, 42),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 42),

    # Node 71
    (3, HUFFMAN_EMIT_SYMBOL, 44),
    (6, HUFFMAN_EMIT_SYMBOL, 44),
    (10, HUFFMAN_EMIT_SYMBOL, 44),
    (15, HUFFMAN_EMIT_SYMBOL, 44),
    (24, HUFFMAN_EMIT_SYMBOL, 44),
    (31, HUFFMAN_EMIT_SYMBOL, 44),
    (41, HUFFMAN_EMIT_SYMBOL, 44),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 44),
    (3, HUFFMAN_EMIT_SYMBOL, 59),
    (6, HUFFMAN_EMIT_SYMBOL, 59),
    (10, HUFFMAN_EMIT_SYMBOL, 59),
    (15, HUFFMAN_EMIT_SYMBOL, 59),
    (24, HUFFMAN_EMIT_SYMBOL, 59),
    (31, HUFFMAN_EMIT_SYMBOL, 59),
    (41, HUFFMAN_EMIT_SYMBOL, 59),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 59),

    # Node 72
    (2, HUFFMAN_EMIT_SYMBOL, 88),
    (9, HUFFMAN_EMIT_SYMBOL, 88),
    (23, HUFFMAN_EMIT_SYMBOL, 88),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 88),
    (2, HUFFMAN_EMIT_SYMBOL, 90),
    (9, HUFFMAN_EMIT_SYMBOL, 90),
    (23, HUFFMAN_EMIT_SYMBOL, 90),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 90),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 33),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 34),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 40),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 41),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 63),
    (80, 0, 0),
    (82, 0, 0),
    (84, 0, 0),

    # Node 73
    (3, HUFFMAN_EMIT_SYMBOL, 88),
    (6, HUFFMAN_EMIT_SYMBOL, 88),
    (10, HUFFMAN_EMIT_SYMBOL, 88),
    (15, HUFFMAN_EMIT_SYMBOL, 88),
    (24, HUFFMAN_EMIT_SYMBOL, 88),
    (31, HUFFMAN_EMIT_SYMBOL, 88),
    (41, HUFFMAN_EMIT_SYMBOL, 88),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 88),
    (3, HUFFMAN_EMIT_SYMBOL, 90),
    (6, HUFFMAN_EMIT_SYMBOL, 90),
    (10, HUFFMAN_EMIT_SYMBOL, 90),
    (15, HUFFMAN_EMIT_SYMBOL, 90),
    (24, HUFFMAN_EMIT_SYMBOL, 90),
    (31, HUFFMAN_EMIT_SYMBOL, 90),
    (41, HUFFMAN_EMIT_SYMBOL, 90),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 90),

    # Node 74
    (1, HUFFMAN_EMIT_SYMBOL, 33),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 33),
    (1, HUFFMAN_EMIT_SYMBOL, 34),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 34),
    (1, HUFFMAN_EMIT_SYMBOL, 40),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 40),
    (1, HUFFMAN_EMIT_SYMBOL, 41),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 41),
    (1, HUFFMAN_EMIT_SYMBOL, 63),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 63),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 39),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 43),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 124),
    (83, 0, 0),
    (85, 0, 0),
    (88, 0, 0),

    # Node 75
    (2, HUFFMAN_EMIT_SYMBOL, 33),
    (9, HUFFMAN_EMIT_SYMBOL, 33),
    (23, HUFFMAN_EMIT_SYMBOL, 33),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 33),
    (2, HUFFMAN_EMIT_SYMBOL, 34),
    (9, HUFFMAN_EMIT_SYMBOL, 34),
    (23, HUFFMAN_EMIT_SYMBOL, 34),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 34),
    (2, HUFFMAN_EMIT_SYMBOL, 40),
    (9, HUFFMAN_EMIT_SYMBOL, 40),
    (23, HUFFMAN_EMIT_SYMBOL, 40),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 40),
    (2, HUFFMAN_EMIT_SYMBOL, 41),
    (9, HUFFMAN_EMIT_SYMBOL, 41),
    (23, HUFFMAN_EMIT_SYMBOL, 41),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 41),

    # Node 76
    (3, HUFFMAN_EMIT_SYMBOL, 33),
    (6, HUFFMAN_EMIT_SYMBOL, 33),
    (10, HUFFMAN_EMIT_SYMBOL, 33),
    (15, HUFFMAN_EMIT_SYMBOL, 33),
    (24, HUFFMAN_EMIT_SYMBOL, 33),
    (31, HUFFMAN_EMIT_SYMBOL, 33),
    (41, HUFFMAN_EMIT_SYMBOL, 33),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 33),
    (3, HUFFMAN_EMIT_SYMBOL, 34),
    (6, HUFFMAN_EMIT_SYMBOL, 34),
    (10, HUFFMAN_EMIT_SYMBOL, 34),
    (15, HUFFMAN_EMIT_SYMBOL, 34),
    (24, HUFFMAN_EMIT_SYMBOL, 34),
    (31, HUFFMAN_EMIT_SYMBOL, 34),
    (41, HUFFMAN_EMIT_SYMBOL, 34),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 34),

    # Node 77
    (3, HUFFMAN_EMIT_SYMBOL, 40),
    (6, HUFFMAN_EMIT_SYMBOL, 40),
    (10, HUFFMAN_EMIT_SYMBOL, 40),
    (15, HUFFMAN_EMIT_SYMBOL, 40),
    (24, HUFFMAN_EMIT_SYMBOL, 40),
    (31, HUFFMAN_EMIT_SYMBOL, 40),
    (41, HUFFMAN_EMIT_SYMBOL, 40),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 40),
    (3, HUFFMAN_EMIT_SYMBOL, 41),
    (6, HUFFMAN_EMIT_SYMBOL, 41),
    (10, HUFFMAN_EMIT_SYMBOL, 41),
    (15, HUFFMAN_EMIT_SYMBOL, 41),
    (24, HUFFMAN_EMIT_SYMBOL, 41),
    (31, HUFFMAN_EMIT_SYMBOL, 41),
    (41, HUFFMAN_EMIT_SYMBOL, 41),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 41),

    # Node 78
    (2, HUFFMAN_EMIT_SYMBOL, 63),
    (9, HUFFMAN_EMIT_SYMBOL, 63),
    (23, HUFFMAN_EMIT_SYMBOL, 63),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 63),
    (1, HUFFMAN_EMIT_SYMBOL, 39),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 39),
    (1, HUFFMAN_EMIT_SYMBOL, 43),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 43),
    (1, HUFFMAN_EMIT_SYMBOL, 124),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 124),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 35),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 62),
    (86, 0, 0),
    (87, 0, 0),
    (89, 0, 0),
    (90, 0, 0),

    # Node 79
    (3, HUFFMAN_EMIT_SYMBOL, 63),
    (6, HUFFMAN_EMIT_SYMBOL, 63),
    (10, HUFFMAN_EMIT_SYMBOL, 63),
    (15, HUFFMAN_EMIT_SYMBOL, 63),
    (24, HUFFMAN_EMIT_SYMBOL, 63),
    (31, HUFFMAN_EMIT_SYMBOL, 63),
    (41, HUFFMAN_EMIT_SYMBOL, 63),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 63),
    (2, HUFFMAN_EMIT_SYMBOL, 39),
    (9, HUFFMAN_EMIT_SYMBOL, 39),
    (23, HUFFMAN_EMIT_SYMBOL, 39),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 39),
    (2, HUFFMAN_EMIT_SYMBOL, 43),
    (9, HUFFMAN_EMIT_SYMBOL, 43),
    (23, HUFFMAN_EMIT_SYMBOL, 43),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 43),

    # Node 80
    (3, HUFFMAN_EMIT_SYMBOL, 39),
    (6, HUFFMAN_EMIT_SYMBOL, 39),
    (10, HUFFMAN_EMIT_SYMBOL, 39),
    (15, HUFFMAN_EMIT_SYMBOL, 39),
    (24, HUFFMAN_EMIT_SYMBOL, 39),
    (31, HUFFMAN_EMIT_SYMBOL, 39),
    (41, HUFFMAN_EMIT_SYMBOL, 39),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 39),
    (3, HUFFMAN_EMIT_SYMBOL, 43),
    (6, HUFFMAN_EMIT_SYMBOL, 43),
    (10, HUFFMAN_EMIT_SYMBOL, 43),
    (15, HUFFMAN_EMIT_SYMBOL, 43),
    (24, HUFFMAN_EMIT_SYMBOL, 43),
    (31, HUFFMAN_EMIT_SYMBOL, 43),
    (41, HUFFMAN_EMIT_SYMBOL, 43),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 43),

    # Node 81
    (2, HUFFMAN_EMIT_SYMBOL, 124),
    (9, HUFFMAN_EMIT_SYMBOL, 124),
    (23, HUFFMAN_EMIT_SYMBOL, 124),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 124),
    (1, HUFFMAN_EMIT_SYMBOL, 35),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 35),
    (1, HUFFMAN_EMIT_SYMBOL, 62),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 62),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 0),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 36),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 64),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 91),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 93),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 126),
    (91, 0, 0),
    (92, 0, 0),

    # Node 82
    (3, HUFFMAN_EMIT_SYMBOL, 124),
    (6, HUFFMAN_EMIT_SYMBOL, 124),
    (10, HUFFMAN_EMIT_SYMBOL, 124),
    (15, HUFFMAN_EMIT_SYMBOL, 124),
    (24, HUFFMAN_EMIT_SYMBOL, 124),
    (31, HUFFMAN_EMIT_SYMBOL, 124),
    (41, HUFFMAN_EMIT_SYMBOL, 124),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 124),
    (2, HUFFMAN_EMIT_SYMBOL, 35),
    (9, HUFFMAN_EMIT_SYMBOL, 35),
    (23, HUFFMAN_EMIT_SYMBOL, 35),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 35),
    (2, HUFFMAN_EMIT_SYMBOL, 62),
    (9, HUFFMAN_EMIT_SYMBOL, 62),
    (23, HUFFMAN_EMIT_SYMBOL, 62),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 62),

    # Node 83
    (3, HUFFMAN_EMIT_SYMBOL, 35),
    (6, HUFFMAN_EMIT_SYMBOL, 35),
    (10, HUFFMAN_EMIT_SYMBOL, 35),
    (15, HUFFMAN_EMIT_SYMBOL, 35),
    (24, HUFFMAN_EMIT_SYMBOL, 35),
    (31, HUFFMAN_EMIT_SYMBOL, 35),
    (41, HUFFMAN_EMIT_SYMBOL, 35),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 35),
    (3, HUFFMAN_EMIT_SYMBOL, 62),
    (6, HUFFMAN_EMIT_SYMBOL, 62),
    (10, HUFFMAN_EMIT_SYMBOL, 62),
    (15, HUFFMAN_EMIT_SYMBOL, 62),
    (24, HUFFMAN_EMIT_SYMBOL, 62),
    (31, HUFFMAN_EMIT_SYMBOL, 62),
    (41, HUFFMAN_EMIT_SYMBOL, 62),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 62),

    # Node 84
    (1, HUFFMAN_EMIT_SYMBOL, 0),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 0),
    (1, HUFFMAN_EMIT_SYMBOL, 36),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 36),
    (1, HUFFMAN_EMIT_SYMBOL, 64),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 64),
    (1, HUFFMAN_EMIT_SYMBOL, 91),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 91),
    (1, HUFFMAN_EMIT_SYMBOL, 93),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 93),
    (1, HUFFMAN_EMIT_SYMBOL, 126),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 126),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 94),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 125),
    (93, 0, 0),
    (94, 0, 0),

    # Node 85
    (2, HUFFMAN_EMIT_SYMBOL, 0),
    (9, HUFFMAN_EMIT_SYMBOL, 0),
    (23, HUFFMAN_EMIT_SYMBOL, 0),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 0),
    (2, HUFFMAN_EMIT_SYMBOL, 36),
    (9, HUFFMAN_EMIT_SYMBOL, 36),
    (23, HUFFMAN_EMIT_SYMBOL, 36),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 36),
    (2, HUFFMAN_EMIT_SYMBOL, 64),
    (9, HUFFMAN_EMIT_SYMBOL, 64),
    (23, HUFFMAN_EMIT_SYMBOL, 64),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 64),
    (2, HUFFMAN_EMIT_SYMBOL, 91),
    (9, HUFFMAN_EMIT_SYMBOL, 91),
    (23, HUFFMAN_EMIT_SYMBOL, 91),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 91),

    # Node 86
    (3, HUFFMAN_EMIT_SYMBOL, 0),
    (6, HUFFMAN_EMIT_SYMBOL, 0),
    (10, HUFFMAN_EMIT_SYMBOL, 0),
    (15, HUFFMAN_EMIT_SYMBOL, 0),
    (24, HUFFMAN_EMIT_SYMBOL, 0),
    (31, HUFFMAN_EMIT_SYMBOL, 0),
    (41, HUFFMAN_EMIT_SYMBOL, 0),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 0),
    (3, HUFFMAN_EMIT_SYMBOL, 36),
    (6, HUFFMAN_EMIT_SYMBOL, 36),
    (10, HUFFMAN_EMIT_SYMBOL, 36),
    (15, HUFFMAN_EMIT_SYMBOL, 36),
    (24, HUFFMAN_EMIT_SYMBOL, 36),
    (31, HUFFMAN_EMIT_SYMBOL, 36),
    (41, HUFFMAN_EMIT_SYMBOL, 36),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 36),

    # Node 87
    (3, HUFFMAN_EMIT_SYMBOL, 64),
    (6, HUFFMAN_EMIT_SYMBOL, 64),
    (10, HUFFMAN_EMIT_SYMBOL, 64),
    (15, HUFFMAN_EMIT_SYMBOL, 64),
    (24, HUFFMAN_EMIT_SYMBOL, 64),
    (31, HUFFMAN_EMIT_SYMBOL, 64),
    (41, HUFFMAN_EMIT_SYMBOL, 64),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 64),
    (3, HUFFMAN_EMIT_SYMBOL, 91),
    (6, HUFFMAN_EMIT_SYMBOL, 91),
    (10, HUFFMAN_EMIT_SYMBOL, 91),
    (15, HUFFMAN_EMIT_SYMBOL, 91),
    (24, HUFFMAN_EMIT_SYMBOL, 91),
    (31, HUFFMAN_EMIT_SYMBOL, 91),
    (41, HUFFMAN_EMIT_SYMBOL, 91),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 91),

    # Node 88
    (2, HUFFMAN_EMIT_SYMBOL, 93),
    (9, HUFFMAN_EMIT_SYMBOL, 93),
    (23, HUFFMAN_EMIT_SYMBOL, 93),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 93),
    (2, HUFFMAN_EMIT_SYMBOL, 126),
    (9, HUFFMAN_EMIT_SYMBOL, 126),
    (23, HUFFMAN_EMIT_SYMBOL, 126),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 126),
    (1, HUFFMAN_EMIT_SYMBOL, 94),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 94),
    (1, HUFFMAN_EMIT_SYMBOL, 125),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 125),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 60),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 96),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 123),
    (95, 0, 0),

    # Node 89
    (3, HUFFMAN_EMIT_SYMBOL, 93),
    (6, HUFFMAN_EMIT_SYMBOL, 93),
    (10, HUFFMAN_EMIT_SYMBOL, 93),
    (15, HUFFMAN_EMIT_SYMBOL, 93),
    (24, HUFFMAN_EMIT_SYMBOL, 93),
    (31, HUFFMAN_EMIT_SYMBOL, 93),
    (41, HUFFMAN_EMIT_SYMBOL, 93),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 93),
    (3, HUFFMAN_EMIT_SYMBOL, 126),
    (6, HUFFMAN_EMIT_SYMBOL, 126),
    (10, HUFFMAN_EMIT_SYMBOL, 126),
    (15, HUFFMAN_EMIT_SYMBOL, 126),
    (24, HUFFMAN_EMIT_SYMBOL, 126),
    (31, HUFFMAN_EMIT_SYMBOL, 126),
    (41, HUFFMAN_EMIT_SYMBOL, 126),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 126),

    # Node 90
    (2, HUFFMAN_EMIT_SYMBOL, 94),
    (9, HUFFMAN_EMIT_SYMBOL, 94),
    (23, HUFFMAN_EMIT_SYMBOL, 94),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 94),
    (2, HUFFMAN_EMIT_SYMBOL, 125),
    (9, HUFFMAN_EMIT_SYMBOL, 125),
    (23, HUFFMAN_EMIT_SYMBOL, 125),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 125),
    (1, HUFFMAN_EMIT_SYMBOL, 60),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 60),
    (1, HUFFMAN_EMIT_SYMBOL, 96),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 96),
    (1, HUFFMAN_EMIT_SYMBOL, 123),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 123),
    (96, 0, 0),
    (110, 0, 0),

    # Node 91
    (3, HUFFMAN_EMIT_SYMBOL, 94),
    (6, HUFFMAN_EMIT_SYMBOL, 94),
    (10, HUFFMAN_EMIT_SYMBOL, 94),
    (15, HUFFMAN_EMIT_SYMBOL, 94),
    (24, HUFFMAN_EMIT_SYMBOL, 94),
    (31, HUFFMAN_EMIT_SYMBOL, 94),
    (41, HUFFMAN_EMIT_SYMBOL, 94),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 94),
    (3, HUFFMAN_EMIT_SYMBOL, 125),
    (6, HUFFMAN_EMIT_SYMBOL, 125),
    (10, HUFFMAN_EMIT_SYMBOL, 125),
    (15, HUFFMAN_EMIT_SYMBOL, 125),
    (24, HUFFMAN_EMIT_SYMBOL, 125),
    (31, HUFFMAN_EMIT_SYMBOL, 125),
    (41, HUFFMAN_EMIT_SYMBOL, 125),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 125),

    # Node 92
    (2, HUFFMAN_EMIT_SYMBOL, 60),
    (9, HUFFMAN_EMIT_SYMBOL, 60),
    (23, HUFFMAN_EMIT_SYMBOL, 60),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 60),
    (2, HUFFMAN_EMIT_SYMBOL, 96),
    (9, HUFFMAN_EMIT_SYMBOL, 96),
    (23, HUFFMAN_EMIT_SYMBOL, 96),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 96),
    (2, HUFFMAN_EMIT_SYMBOL, 123),
    (9, HUFFMAN_EMIT_SYMBOL, 123),
    (23, HUFFMAN_EMIT_SYMBOL, 123),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 123),
    (97, 0, 0),
    (101, 0, 0),
    (111, 0, 0),
    (133, 0, 0),

    # Node 93
    (3, HUFFMAN_EMIT_SYMBOL, 60),
    (6, HUFFMAN_EMIT_SYMBOL, 60),
    (10, HUFFMAN_EMIT_SYMBOL, 60),
    (15, HUFFMAN_EMIT_SYMBOL, 60),
    (24, HUFFMAN_EMIT_SYMBOL, 60),
    (31, HUFFMAN_EMIT_SYMBOL, 60),
    (41, HUFFMAN_EMIT_SYMBOL, 60),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 60),
    (3, HUFFMAN_EMIT_SYMBOL, 96),
    (6, HUFFMAN_EMIT_SYMBOL, 96),
    (10, HUFFMAN_EMIT_SYMBOL, 96),
    (15, HUFFMAN_EMIT_SYMBOL, 96),
    (24, HUFFMAN_EMIT_SYMBOL, 96),
    (31, HUFFMAN_EMIT_SYMBOL, 96),
    (41, HUFFMAN_EMIT_SYMBOL, 96),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 96),

    # Node 94
    (3, HUFFMAN_EMIT_SYMBOL, 123),
    (6, HUFFMAN_EMIT_SYMBOL, 123),
    (10, HUFFMAN_EMIT_SYMBOL, 123),
    (15, HUFFMAN_EMIT_SYMBOL, 123),
    (24, HUFFMAN_EMIT_SYMBOL, 123),
    (31, HUFFMAN_EMIT_SYMBOL, 123),
    (41, HUFFMAN_EMIT_SYMBOL, 123),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 123),
    (98, 0, 0),
    (99, 0, 0),
    (102, 0, 0),
    (105, 0, 0),
    (112, 0, 0),
    (119, 0, 0),
    (134, 0, 0),
    (153, 0, 0),

    # Node 95
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 92),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 195),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 208),
    (100, 0, 0),
    (103, 0, 0),
    (104, 0, 0),
    (106, 0, 0),
    (107, 0, 0),
    (113, 0, 0),
    (116, 0, 0),
    (120, 0, 0),
    (126, 0, 0),
    (135, 0, 0),
    (142, 0, 0),
    (154, 0, 0),
    (169, 0, 0),

    # Node 96
    (1, HUFFMAN_EMIT_SYMBOL, 92),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 92),
    (1, HUFFMAN_EMIT_SYMBOL, 195),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 195),
    (1, HUFFMAN_EMIT_SYMBOL, 208),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 208),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 128),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 130),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 131),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 162),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 184),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 194),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 224),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 226),
    (108, 0, 0),
    (109, 0, 0),

    # Node 97
    (2, HUFFMAN_EMIT_SYMBOL, 92),
    (9, HUFFMAN_EMIT_SYMBOL, 92),
    (23, HUFFMAN_EMIT_SYMBOL, 92),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 92),
    (2, HUFFMAN_EMIT_SYMBOL, 195),
    (9, HUFFMAN_EMIT_SYMBOL, 195),
    (23, HUFFMAN_EMIT_SYMBOL, 195),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 195),
    (2, HUFFMAN_EMIT_SYMBOL, 208),
    (9, HUFFMAN_EMIT_SYMBOL, 208),
    (23, HUFFMAN_EMIT_SYMBOL, 208),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 208),
    (1, HUFFMAN_EMIT_SYMBOL, 128),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 128),
    (1, HUFFMAN_EMIT_SYMBOL, 130),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 130),

    # Node 98
    (3, HUFFMAN_EMIT_SYMBOL, 92),
    (6, HUFFMAN_EMIT_SYMBOL, 92),
    (10, HUFFMAN_EMIT_SYMBOL, 92),
    (15, HUFFMAN_EMIT_SYMBOL, 92),
    (24, HUFFMAN_EMIT_SYMBOL, 92),
    (31, HUFFMAN_EMIT_SYMBOL, 92),
    (41, HUFFMAN_EMIT_SYMBOL, 92),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 92),
    (3, HUFFMAN_EMIT_SYMBOL, 195),
    (6, HUFFMAN_EMIT_SYMBOL, 195),
    (10, HUFFMAN_EMIT_SYMBOL, 195),
    (15, HUFFMAN_EMIT_SYMBOL, 195),
    (24, HUFFMAN_EMIT_SYMBOL, 195),
    (31, HUFFMAN_EMIT_SYMBOL, 195),
    (41, HUFFMAN_EMIT_SYMBOL, 195),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 195),

    # Node 99
    (3, HUFFMAN_EMIT_SYMBOL, 208),
    (6, HUFFMAN_EMIT_SYMBOL, 208),
    (10, HUFFMAN_EMIT_SYMBOL, 208),
    (15, HUFFMAN_EMIT_SYMBOL, 208),
    (24, HUFFMAN_EMIT_SYMBOL, 208),
    (31, HUFFMAN_EMIT_SYMBOL, 208),
    (41, HUFFMAN_EMIT_SYMBOL, 208),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 208),
    (2, HUFFMAN_EMIT_SYMBOL, 128),
    (9, HUFFMAN_EMIT_SYMBOL, 128),
    (23, HUFFMAN_EMIT_SYMBOL, 128),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 128),
    (2, HUFFMAN_EMIT_SYMBOL, 130),
    (9, HUFFMAN_EMIT_SYMBOL, 130),
    (23, HUFFMAN_EMIT_SYMBOL, 130),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 130),

    # Node 100
    (3, HUFFMAN_EMIT_SYMBOL, 128),
    (6, HUFFMAN_EMIT_SYMBOL, 128),
    (10, HUFFMAN_EMIT_SYMBOL, 128),
    (15, HUFFMAN_EMIT_SYMBOL, 128),
    (24, HUFFMAN_EMIT_SYMBOL, 128),
    (31, HUFFMAN_EMIT_SYMBOL, 128),
    (41, HUFFMAN_EMIT_SYMBOL, 128),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 128),
    (3, HUFFMAN_EMIT_SYMBOL, 130),
    (6, HUFFMAN_EMIT_SYMBOL, 130),
    (10, HUFFMAN_EMIT_SYMBOL, 130),
    (15, HUFFMAN_EMIT_SYMBOL, 130),
    (24, HUFFMAN_EMIT_SYMBOL, 130),
    (31, HUFFMAN_EMIT_SYMBOL, 130),
    (41, HUFFMAN_EMIT_SYMBOL, 130),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 130),

    # Node 101
    (1, HUFFMAN_EMIT_SYMBOL, 131),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 131),
    (1, HUFFMAN_EMIT_SYMBOL, 162),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 162),
    (1, HUFFMAN_EMIT_SYMBOL, 184),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 184),
    (1, HUFFMAN_EMIT_SYMBOL, 194),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 194),
    (1, HUFFMAN_EMIT_SYMBOL, 224),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 224),
    (1, HUFFMAN_EMIT_SYMBOL, 226),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 226),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 153),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 161),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 167),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 172),

    # Node 102
    (2, HUFFMAN_EMIT_SYMBOL, 131),
    (9, HUFFMAN_EMIT_SYMBOL, 131),
    (23, HUFFMAN_EMIT_SYMBOL, 131),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 131),
    (2, HUFFMAN_EMIT_SYMBOL, 162),
    (9, HUFFMAN_EMIT_SYMBOL, 162),
    (23, HUFFMAN_EMIT_SYMBOL, 162),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 162),
    (2, HUFFMAN_EMIT_SYMBOL, 184),
    (9, HUFFMAN_EMIT_SYMBOL, 184),
    (23, HUFFMAN_EMIT_SYMBOL, 184),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 184),
    (2, HUFFMAN_EMIT_SYMBOL, 194),
    (9, HUFFMAN_EMIT_SYMBOL, 194),
    (23, HUFFMAN_EMIT_SYMBOL, 194),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 194),

    # Node 103
    (3, HUFFMAN_EMIT_SYMBOL, 131),
    (6, HUFFMAN_EMIT_SYMBOL, 131),
    (10, HUFFMAN_EMIT_SYMBOL, 131),
    (15, HUFFMAN_EMIT_SYMBOL, 131),
    (24, HUFFMAN_EMIT_SYMBOL, 131),
    (31, HUFFMAN_EMIT_SYMBOL, 131),
    (41, HUFFMAN_EMIT_SYMBOL, 131),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 131),
    (3, HUFFMAN_EMIT_SYMBOL, 162),
    (6, HUFFMAN_EMIT_SYMBOL, 162),
    (10, HUFFMAN_EMIT_SYMBOL, 162),
    (15, HUFFMAN_EMIT_SYMBOL, 162),
    (24, HUFFMAN_EMIT_SYMBOL, 162),
    (31, HUFFMAN_EMIT_SYMBOL, 162),
    (41, HUFFMAN_EMIT_SYMBOL, 162),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 162),

    # Node 104
    (3, HUFFMAN_EMIT_SYMBOL, 184),
    (6, HUFFMAN_EMIT_SYMBOL, 184),
    (10, HUFFMAN_EMIT_SYMBOL, 184),
    (15, HUFFMAN_EMIT_SYMBOL, 184),
    (24, HUFFMAN_EMIT_SYMBOL, 184),
    (31, HUFFMAN_EMIT_SYMBOL, 184),
    (41, HUFFMAN_EMIT_SYMBOL, 184),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 184),
    (3, HUFFMAN_EMIT_SYMBOL, 194),
    (6, HUFFMAN_EMIT_SYMBOL, 194),
    (10, HUFFMAN_EMIT_SYMBOL, 194),
    (15, HUFFMAN_EMIT_SYMBOL, 194),
    (24, HUFFMAN_EMIT_SYMBOL, 194),
    (31, HUFFMAN_EMIT_SYMBOL, 194),
    (41, HUFFMAN_EMIT_SYMBOL, 194),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 194),

    # Node 105
    (2, HUFFMAN_EMIT_SYMBOL, 224),
    (9, HUFFMAN_EMIT_SYMBOL, 224),
    (23, HUFFMAN_EMIT_SYMBOL, 224),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 224),
    (2, HUFFMAN_EMIT_SYMBOL, 226),
    (9, HUFFMAN_EMIT_SYMBOL, 226),
    (23, HUFFMAN_EMIT_SYMBOL, 226),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 226),
    (1, HUFFMAN_EMIT_SYMBOL, 153),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 153),
    (1, HUFFMAN_EMIT_SYMBOL, 161),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 161),
    (1, HUFFMAN_EMIT_SYMBOL, 167),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 167),
    (1, HUFFMAN_EMIT_SYMBOL, 172),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 172),

    # Node 106
    (3, HUFFMAN_EMIT_SYMBOL, 224),
    (6, HUFFMAN_EMIT_SYMBOL, 224),
    (10, HUFFMAN_EMIT_SYMBOL, 224),
    (15, HUFFMAN_EMIT_SYMBOL, 224),
    (24, HUFFMAN_EMIT_SYMBOL, 224),
    (31, HUFFMAN_EMIT_SYMBOL, 224),
    (41, HUFFMAN_EMIT_SYMBOL, 224),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 224),
    (3, HUFFMAN_EMIT_SYMBOL, 226),
    (6, HUFFMAN_EMIT_SYMBOL, 226),
    (10, HUFFMAN_EMIT_SYMBOL, 226),
    (15, HUFFMAN_EMIT_SYMBOL, 226),
    (24, HUFFMAN_EMIT_SYMBOL, 226),
    (31, HUFFMAN_EMIT_SYMBOL, 226),
    (41, HUFFMAN_EMIT_SYMBOL, 226),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 226),

    # Node 107
    (2, HUFFMAN_EMIT_SYMBOL, 153),
    (9, HUFFMAN_EMIT_SYMBOL, 153),
    (23, HUFFMAN_EMIT_SYMBOL, 153),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 153),
    (2, HUFFMAN_EMIT_SYMBOL, 161),
    (9, HUFFMAN_EMIT_SYMBOL, 161),
    (23, HUFFMAN_EMIT_SYMBOL, 161),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 161),
    (2, HUFFMAN_EMIT_SYMBOL, 167),
    (9, HUFFMAN_EMIT_SYMBOL, 167),
    (23, HUFFMAN_EMIT_SYMBOL, 167),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 167),
    (2, HUFFMAN_EMIT_SYMBOL, 172),
    (9, HUFFMAN_EMIT_SYMBOL, 172),
    (23, HUFFMAN_EMIT_SYMBOL, 172),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 172),

    # Node 108
    (3, HUFFMAN_EMIT_SYMBOL, 153),
    (6, HUFFMAN_EMIT_SYMBOL, 153),
    (10, HUFFMAN_EMIT_SYMBOL, 153),
    (15, HUFFMAN_EMIT_SYMBOL, 153),
    (24, HUFFMAN_EMIT_SYMBOL, 153),
    (31, HUFFMAN_EMIT_SYMBOL, 153),
    (41, HUFFMAN_EMIT_SYMBOL, 153),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 153),
    (3, HUFFMAN_EMIT_SYMBOL, 161),
    (6, HUFFMAN_EMIT_SYMBOL, 161),
    (10, HUFFMAN_EMIT_SYMBOL, 161),
    (15, HUFFMAN_EMIT_SYMBOL, 161),
    (24, HUFFMAN_EMIT_SYMBOL, 161),
    (31, HUFFMAN_EMIT_SYMBOL, 161),
    (41, HUFFMAN_EMIT_SYMBOL, 161),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 161),

    # Node 109
    (3, HUFFMAN_EMIT_SYMBOL, 167),
    (6, HUFFMAN_EMIT_SYMBOL, 167),
    (10, HUFFMAN_EMIT_SYMBOL, 167),
    (15, HUFFMAN_EMIT_SYMBOL, 167),
    (24, HUFFMAN_EMIT_SYMBOL, 167),
    (31, HUFFMAN_EMIT_SYMBOL, 167),
    (41, HUFFMAN_EMIT_SYMBOL, 167),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 167),
    (3, HUFFMAN_EMIT_SYMBOL, 172),
    (6, HUFFMAN_EMIT_SYMBOL, 172),
    (10, HUFFMAN_EMIT_SYMBOL, 172),
    (15, HUFFMAN_EMIT_SYMBOL, 172),
    (24, HUFFMAN_EMIT_SYMBOL, 172),
    (31, HUFFMAN_EMIT_SYMBOL, 172),
    (41, HUFFMAN_EMIT_SYMBOL, 172),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 172),

    # Node 110
    (114, 0, 0),
    (115, 0, 0),
    (117, 0, 0),
    (118, 0, 0),
    (121, 0, 0),
    (123, 0, 0),
    (127, 0, 0),
    (130, 0, 0),
    (136, 0, 0),
    (139, 0, 0),
    (143, 0, 0),
    (146, 0, 0),
    (155, 0, 0),
    (162, 0, 0),
    (170, 0, 0),
    (180, 0, 0),

    # Node 111
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 176),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 177),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 179),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 209),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 216),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 217),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 227),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 229),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 230),
    (122, 0, 0),
    (124, 0, 0),
    (125, 0, 0),
    (128, 0, 0),
    (129, 0, 0),
    (131, 0, 0),
    (132, 0, 0),

    # Node 112
    (1, HUFFMAN_EMIT_SYMBOL, 176),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 176),
    (1, HUFFMAN_EMIT_SYMBOL, 177),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 177),
    (1, HUFFMAN_EMIT_SYMBOL, 179),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 179),
    (1, HUFFMAN_EMIT_SYMBOL, 209),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 209),
    (1, HUFFMAN_EMIT_SYMBOL, 216),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 216),
    (1, HUFFMAN_EMIT_SYMBOL, 217),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 217),
    (1, HUFFMAN_EMIT_SYMBOL, 227),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 227),
    (1, HUFFMAN_EMIT_SYMBOL, 229),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 229),

    # Node 113
    (2, HUFFMAN_EMIT_SYMBOL, 176),
    (9, HUFFMAN_EMIT_SYMBOL, 176),
    (23, HUFFMAN_EMIT_SYMBOL, 176),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 176),
    (2, HUFFMAN_EMIT_SYMBOL, 177),
    (9, HUFFMAN_EMIT_SYMBOL, 177),
    (23, HUFFMAN_EMIT_SYMBOL, 177),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 177),
    (2, HUFFMAN_EMIT_SYMBOL, 179),
    (9, HUFFMAN_EMIT_SYMBOL, 179),
    (23, HUFFMAN_EMIT_SYMBOL, 179),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 179),
    (2, HUFFMAN_EMIT_SYMBOL, 209),
    (9, HUFFMAN_EMIT_SYMBOL, 209),
    (23, HUFFMAN_EMIT_SYMBOL, 209),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 209),

    # Node 114
    (3, HUFFMAN_EMIT_SYMBOL, 176),
    (6, HUFFMAN_EMIT_SYMBOL, 176),
    (10, HUFFMAN_EMIT_SYMBOL, 176),
    (15, HUFFMAN_EMIT_SYMBOL, 176),
    (24, HUFFMAN_EMIT_SYMBOL, 176),
    (31, HUFFMAN_EMIT_SYMBOL, 176),
    (41, HUFFMAN_EMIT_SYMBOL, 176),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 176),
    (3, HUFFMAN_EMIT_SYMBOL, 177),
    (6, HUFFMAN_EMIT_SYMBOL, 177),
    (10, HUFFMAN_EMIT_SYMBOL, 177),
    (15, HUFFMAN_EMIT_SYMBOL, 177),
    (24, HUFFMAN_EMIT_SYMBOL, 177),
    (31, HUFFMAN_EMIT_SYMBOL, 177),
    (41, HUFFMAN_EMIT_SYMBOL, 177),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 177),

    # Node 115
    (3, HUFFMAN_EMIT_SYMBOL, 179),
    (6, HUFFMAN_EMIT_SYMBOL, 179),
    (10, HUFFMAN_EMIT_SYMBOL, 179),
    (15, HUFFMAN_EMIT_SYMBOL, 179),
    (24, HUFFMAN_EMIT_SYMBOL, 179),
    (31, HUFFMAN_EMIT_SYMBOL, 179),
    (41, HUFFMAN_EMIT_SYMBOL, 179),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 179),
    (3, HUFFMAN_EMIT_SYMBOL, 209),
    (6, HUFFMAN_EMIT_SYMBOL, 209),
    (10, HUFFMAN_EMIT_SYMBOL, 209),
    (15, HUFFMAN_EMIT_SYMBOL, 209),
    (24, HUFFMAN_EMIT_SYMBOL, 209),
    (31, HUFFMAN_EMIT_SYMBOL, 209),
    (41, HUFFMAN_EMIT_SYMBOL, 209),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 209),

    # Node 116
    (2, HUFFMAN_EMIT_SYMBOL, 216),
    (9, HUFFMAN_EMIT_SYMBOL, 216),
    (23, HUFFMAN_EMIT_SYMBOL, 216),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 216),
    (2, HUFFMAN_EMIT_SYMBOL, 217),
    (9, HUFFMAN_EMIT_SYMBOL, 217),
    (23, HUFFMAN_EMIT_SYMBOL, 217),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 217),
    (2, HUFFMAN_EMIT_SYMBOL, 227),
    (9, HUFFMAN_EMIT_SYMBOL, 227),
    (23, HUFFMAN_EMIT_SYMBOL, 227),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 227),
    (2, HUFFMAN_EMIT_SYMBOL, 229),
    (9, HUFFMAN_EMIT_SYMBOL, 229),
    (23, HUFFMAN_EMIT_SYMBOL, 229),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 229),

    # Node 117
    (3, HUFFMAN_EMIT_SYMBOL, 216),
    (6, HUFFMAN_EMIT_SYMBOL, 216),
    (10, HUFFMAN_EMIT_SYMBOL, 216),
    (15, HUFFMAN_EMIT_SYMBOL, 216),
    (24, HUFFMAN_EMIT_SYMBOL, 216),
    (31, HUFFMAN_EMIT_SYMBOL, 216),
    (41, HUFFMAN_EMIT_SYMBOL, 216),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 216),
    (3, HUFFMAN_EMIT_SYMBOL, 217),
    (6, HUFFMAN_EMIT_SYMBOL, 217),
    (10, HUFFMAN_EMIT_SYMBOL, 217),
    (15, HUFFMAN_EMIT_SYMBOL, 217),
    (24, HUFFMAN_EMIT_SYMBOL, 217),
    (31, HUFFMAN_EMIT_SYMBOL, 217),
    (41, HUFFMAN_EMIT_SYMBOL, 217),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 217),

    # Node 118
    (3, HUFFMAN_EMIT_SYMBOL, 227),
    (6, HUFFMAN_EMIT_SYMBOL, 227),
    (10, HUFFMAN_EMIT_SYMBOL, 227),
    (15, HUFFMAN_EMIT_SYMBOL, 227),
    (24, HUFFMAN_EMIT_SYMBOL, 227),
    (31, HUFFMAN_EMIT_SYMBOL, 227),
    (41, HUFFMAN_EMIT_SYMBOL, 227),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 227),
    (3, HUFFMAN_EMIT_SYMBOL, 229),
    (6, HUFFMAN_EMIT_SYMBOL, 229),
    (10, HUFFMAN_EMIT_SYMBOL, 229),
    (15, HUFFMAN_EMIT_SYMBOL, 229),
    (24, HUFFMAN_EMIT_SYMBOL, 229),
    (31, HUFFMAN_EMIT_SYMBOL, 229),
    (41, HUFFMAN_EMIT_SYMBOL, 229),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 229),

    # Node 119
    (1, HUFFMAN_EMIT_SYMBOL, 230),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 230),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 129),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 132),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 133),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 134),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 136),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 146),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 154),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 156),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 160),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 163),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 164),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 169),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 170),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 173),

    # Node 120
    (2, HUFFMAN_EMIT_SYMBOL, 230),
    (9, HUFFMAN_EMIT_SYMBOL, 230),
    (23, HUFFMAN_EMIT_SYMBOL, 230),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 230),
    (1, HUFFMAN_EMIT_SYMBOL, 129),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 129),
    (1, HUFFMAN_EMIT_SYMBOL, 132),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 132),
    (1, HUFFMAN_EMIT_SYMBOL, 133),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 133),
    (1, HUFFMAN_EMIT_SYMBOL, 134),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 134),
    (1, HUFFMAN_EMIT_SYMBOL, 136),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 136),
    (1, HUFFMAN_EMIT_SYMBOL, 146),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 146),

    # Node 121
    (3, HUFFMAN_EMIT_SYMBOL, 230),
    (6, HUFFMAN_EMIT_SYMBOL, 230),
    (10, HUFFMAN_EMIT_SYMBOL, 230),
    (15, HUFFMAN_EMIT_SYMBOL, 230),
    (24, HUFFMAN_EMIT_SYMBOL, 230),
    (31, HUFFMAN_EMIT_SYMBOL, 230),
    (41, HUFFMAN_EMIT_SYMBOL, 230),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 230),
    (2, HUFFMAN_EMIT_SYMBOL, 129),
    (9, HUFFMAN_EMIT_SYMBOL, 129),
    (23, HUFFMAN_EMIT_SYMBOL, 129),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 129),
    (2, HUFFMAN_EMIT_SYMBOL, 132),
    (9, HUFFMAN_EMIT_SYMBOL, 132),
    (23, HUFFMAN_EMIT_SYMBOL, 132),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 132),

    # Node 122
    (3, HUFFMAN_EMIT_SYMBOL, 129),
    (6, HUFFMAN_EMIT_SYMBOL, 129),
    (10, HUFFMAN_EMIT_SYMBOL, 129),
    (15, HUFFMAN_EMIT_SYMBOL, 129),
    (24, HUFFMAN_EMIT_SYMBOL, 129),
    (31, HUFFMAN_EMIT_SYMBOL, 129),
    (41, HUFFMAN_EMIT_SYMBOL, 129),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 129),
    (3, HUFFMAN_EMIT_SYMBOL, 132),
    (6, HUFFMAN_EMIT_SYMBOL, 132),
    (10, HUFFMAN_EMIT_SYMBOL, 132),
    (15, HUFFMAN_EMIT_SYMBOL, 132),
    (24, HUFFMAN_EMIT_SYMBOL, 132),
    (31, HUFFMAN_EMIT_SYMBOL, 132),
    (41, HUFFMAN_EMIT_SYMBOL, 132),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 132),

    # Node 123
    (2, HUFFMAN_EMIT_SYMBOL, 133),
    (9, HUFFMAN_EMIT_SYMBOL, 133),
    (23, HUFFMAN_EMIT_SYMBOL, 133),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 133),
    (2, HUFFMAN_EMIT_SYMBOL, 134),
    (9, HUFFMAN_EMIT_SYMBOL, 134),
    (23, HUFFMAN_EMIT_SYMBOL, 134),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 134),
    (2, HUFFMAN_EMIT_SYMBOL, 136),
    (9, HUFFMAN_EMIT_SYMBOL, 136),
    (23, HUFFMAN_EMIT_SYMBOL, 136),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 136),
    (2, HUFFMAN_EMIT_SYMBOL, 146),
    (9, HUFFMAN_EMIT_SYMBOL, 146),
    (23, HUFFMAN_EMIT_SYMBOL, 146),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 146),

    # Node 124
    (3, HUFFMAN_EMIT_SYMBOL, 133),
    (6, HUFFMAN_EMIT_SYMBOL, 133),
    (10, HUFFMAN_EMIT_SYMBOL, 133),
    (15, HUFFMAN_EMIT_SYMBOL, 133),
    (24, HUFFMAN_EMIT_SYMBOL, 133),
    (31, HUFFMAN_EMIT_SYMBOL, 133),
    (41, HUFFMAN_EMIT_SYMBOL, 133),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 133),
    (3, HUFFMAN_EMIT_SYMBOL, 134),
    (6, HUFFMAN_EMIT_SYMBOL, 134),
    (10, HUFFMAN_EMIT_SYMBOL, 134),
    (15, HUFFMAN_EMIT_SYMBOL, 134),
    (24, HUFFMAN_EMIT_SYMBOL, 134),
    (31, HUFFMAN_EMIT_SYMBOL, 134),
    (41, HUFFMAN_EMIT_SYMBOL, 134),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 134),

    # Node 125
    (3, HUFFMAN_EMIT_SYMBOL, 136),
    (6, HUFFMAN_EMIT_SYMBOL, 136),
    (10, HUFFMAN_EMIT_SYMBOL, 136),
    (15, HUFFMAN_EMIT_SYMBOL, 136),
    (24, HUFFMAN_EMIT_SYMBOL, 136),
    (31, HUFFMAN_EMIT_SYMBOL, 136),
    (41, HUFFMAN_EMIT_SYMBOL, 136),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 136),
    (3, HUFFMAN_EMIT_SYMBOL, 146),
    (6, HUFFMAN_EMIT_SYMBOL, 146),
    (10, HUFFMAN_EMIT_SYMBOL, 146),
    (15, HUFFMAN_EMIT_SYMBOL, 146),
    (24, HUFFMAN_EMIT_SYMBOL, 146),
    (31, HUFFMAN_EMIT_SYMBOL, 146),
    (41, HUFFMAN_EMIT_SYMBOL, 146),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 146),

    # Node 126
    (1, HUFFMAN_EMIT_SYMBOL, 154),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 154),
    (1, HUFFMAN_EMIT_SYMBOL, 156),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 156),
    (1, HUFFMAN_EMIT_SYMBOL, 160),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 160),
    (1, HUFFMAN_EMIT_SYMBOL, 163),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 163),
    (1, HUFFMAN_EMIT_SYMBOL, 164),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 164),
    (1, HUFFMAN_EMIT_SYMBOL, 169),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 169),
    (1, HUFFMAN_EMIT_SYMBOL, 170),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 170),
    (1, HUFFMAN_EMIT_SYMBOL, 173),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 173),

    # Node 127
    (2, HUFFMAN_EMIT_SYMBOL, 154),
    (9, HUFFMAN_EMIT_SYMBOL, 154),
    (23, HUFFMAN_EMIT_SYMBOL, 154),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 154),
    (2, HUFFMAN_EMIT_SYMBOL, 156),
    (9, HUFFMAN_EMIT_SYMBOL, 156),
    (23, HUFFMAN_EMIT_SYMBOL, 156),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 156),
    (2, HUFFMAN_EMIT_SYMBOL, 160),
    (9, HUFFMAN_EMIT_SYMBOL, 160),
    (23, HUFFMAN_EMIT_SYMBOL, 160),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 160),
    (2, HUFFMAN_EMIT_SYMBOL, 163),
    (9, HUFFMAN_EMIT_SYMBOL, 163),
    (23, HUFFMAN_EMIT_SYMBOL, 163),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 163),

    # Node 128
    (3, HUFFMAN_EMIT_SYMBOL, 154),
    (6, HUFFMAN_EMIT_SYMBOL, 154),
    (10, HUFFMAN_EMIT_SYMBOL, 154),
    (15, HUFFMAN_EMIT_SYMBOL, 154),
    (24, HUFFMAN_EMIT_SYMBOL, 154),
    (31, HUFFMAN_EMIT_SYMBOL, 154),
    (41, HUFFMAN_EMIT_SYMBOL, 154),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 154),
    (3, HUFFMAN_EMIT_SYMBOL, 156),
    (6, HUFFMAN_EMIT_SYMBOL, 156),
    (10, HUFFMAN_EMIT_SYMBOL, 156),
    (15, HUFFMAN_EMIT_SYMBOL, 156),
    (24, HUFFMAN_EMIT_SYMBOL, 156),
    (31, HUFFMAN_EMIT_SYMBOL, 156),
    (41, HUFFMAN_EMIT_SYMBOL, 156),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 156),

    # Node 129
    (3, HUFFMAN_EMIT_SYMBOL, 160),
    (6, HUFFMAN_EMIT_SYMBOL, 160),
    (10, HUFFMAN_EMIT_SYMBOL, 160),
    (15, HUFFMAN_EMIT_SYMBOL, 160),
    (24, HUFFMAN_EMIT_SYMBOL, 160),
    (31, HUFFMAN_EMIT_SYMBOL, 160),
    (41, HUFFMAN_EMIT_SYMBOL, 160),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 160),
    (3, HUFFMAN_EMIT_SYMBOL, 163),
    (6, HUFFMAN_EMIT_SYMBOL, 163),
    (10, HUFFMAN_EMIT_SYMBOL, 163),
    (15, HUFFMAN_EMIT_SYMBOL, 163),
    (24, HUFFMAN_EMIT_SYMBOL, 163),
    (31, HUFFMAN_EMIT_SYMBOL, 163),
    (41, HUFFMAN_EMIT_SYMBOL, 163),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 163),

    # Node 130
    (2, HUFFMAN_EMIT_SYMBOL, 164),
    (9, HUFFMAN_EMIT_SYMBOL, 164),
    (23, HUFFMAN_EMIT_SYMBOL, 164),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 164),
    (2, HUFFMAN_EMIT_SYMBOL, 169),
    (9, HUFFMAN_EMIT_SYMBOL, 169),
    (23, HUFFMAN_EMIT_SYMBOL, 169),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 169),
    (2, HUFFMAN_EMIT_SYMBOL, 170),
    (9, HUFFMAN_EMIT_SYMBOL, 170),
    (23, HUFFMAN_EMIT_SYMBOL, 170),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 170),
    (2, HUFFMAN_EMIT_SYMBOL, 173),
    (9, HUFFMAN_EMIT_SYMBOL, 173),
    (23, HUFFMAN_EMIT_SYMBOL, 173),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 173),

    # Node 131
    (3, HUFFMAN_EMIT_SYMBOL, 164),
    (6, HUFFMAN_EMIT_SYMBOL, 164),
    (10, HUFFMAN_EMIT_SYMBOL, 164),
    (15, HUFFMAN_EMIT_SYMBOL, 164),
    (24, HUFFMAN_EMIT_SYMBOL, 164),
    (31, HUFFMAN_EMIT_SYMBOL, 164),
    (41, HUFFMAN_EMIT_SYMBOL, 164),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 164),
    (3, HUFFMAN_EMIT_SYMBOL, 169),
    (6, HUFFMAN_EMIT_SYMBOL, 169),
    (10, HUFFMAN_EMIT_SYMBOL, 169),
    (15, HUFFMAN_EMIT_SYMBOL, 169),
    (24, HUFFMAN_EMIT_SYMBOL, 169),
    (31, HUFFMAN_EMIT_SYMBOL, 169),
    (41, HUFFMAN_EMIT_SYMBOL, 169),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 169),

    # Node 132
    (3, HUFFMAN_EMIT_SYMBOL, 170),
    (6, HUFFMAN_EMIT_SYMBOL, 170),
    (10, HUFFMAN_EMIT_SYMBOL, 170),
    (15, HUFFMAN_EMIT_SYMBOL, 170),
    (24, HUFFMAN_EMIT_SYMBOL, 170),
    (31, HUFFMAN_EMIT_SYMBOL, 170),
    (41, HUFFMAN_EMIT_SYMBOL, 170),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 170),
    (3, HUFFMAN_EMIT_SYMBOL, 173),
    (6, HUFFMAN_EMIT_SYMBOL, 173),
    (10, HUFFMAN_EMIT_SYMBOL, 173),
    (15, HUFFMAN_EMIT_SYMBOL, 173),
    (24, HUFFMAN_EMIT_SYMBOL, 173),
    (31, HUFFMAN_EMIT_SYMBOL, 173),
    (41, HUFFMAN_EMIT_SYMBOL, 173),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 173),

    # Node 133
    (137, 0, 0),
    (138, 0, 0),
    (140, 0, 0),
    (141, 0, 0),
    (144, 0, 0),
    (145, 0, 0),
    (147, 0, 0),
    (150, 0, 0),
    (156, 0, 0),
    (159, 0, 0),
    (163, 0, 0),
    (166, 0, 0),
    (171, 0, 0),
    (174, 0, 0),
    (181, 0, 0),
    (190, 0, 0),

    # Node 134
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 178),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 181),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 185),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 186),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 187),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 189),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 190),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 196),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 198),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 228),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 232),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 233),
    (148, 0, 0),
    (149, 0, 0),
    (151, 0, 0),
    (152, 0, 0),

    # Node 135
    (1, HUFFMAN_EMIT_SYMBOL, 178),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 178),
    (1, HUFFMAN_EMIT_SYMBOL, 181),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 181),
    (1, HUFFMAN_EMIT_SYMBOL, 185),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 185),
    (1, HUFFMAN_EMIT_SYMBOL, 186),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 186),
    (1, HUFFMAN_EMIT_SYMBOL, 187),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 187),
    (1, HUFFMAN_EMIT_SYMBOL, 189),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 189),
    (1, HUFFMAN_EMIT_SYMBOL, 190),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 190),
    (1, HUFFMAN_EMIT_SYMBOL, 196),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 196),

    # Node 136
    (2, HUFFMAN_EMIT_SYMBOL, 178),
    (9, HUFFMAN_EMIT_SYMBOL, 178),
    (23, HUFFMAN_EMIT_SYMBOL, 178),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 178),
    (2, HUFFMAN_EMIT_SYMBOL, 181),
    (9, HUFFMAN_EMIT_SYMBOL, 181),
    (23, HUFFMAN_EMIT_SYMBOL, 181),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 181),
    (2, HUFFMAN_EMIT_SYMBOL, 185),
    (9, HUFFMAN_EMIT_SYMBOL, 185),
    (23, HUFFMAN_EMIT_SYMBOL, 185),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 185),
    (2, HUFFMAN_EMIT_SYMBOL, 186),
    (9, HUFFMAN_EMIT_SYMBOL, 186),
    (23, HUFFMAN_EMIT_SYMBOL, 186),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 186),

    # Node 137
    (3, HUFFMAN_EMIT_SYMBOL, 178),
    (6, HUFFMAN_EMIT_SYMBOL, 178),
    (10, HUFFMAN_EMIT_SYMBOL, 178),
    (15, HUFFMAN_EMIT_SYMBOL, 178),
    (24, HUFFMAN_EMIT_SYMBOL, 178),
    (31, HUFFMAN_EMIT_SYMBOL, 178),
    (41, HUFFMAN_EMIT_SYMBOL, 178),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 178),
    (3, HUFFMAN_EMIT_SYMBOL, 181),
    (6, HUFFMAN_EMIT_SYMBOL, 181),
    (10, HUFFMAN_EMIT_SYMBOL, 181),
    (15, HUFFMAN_EMIT_SYMBOL, 181),
    (24, HUFFMAN_EMIT_SYMBOL, 181),
    (31, HUFFMAN_EMIT_SYMBOL, 181),
    (41, HUFFMAN_EMIT_SYMBOL, 181),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 181),

    # Node 138
    (3, HUFFMAN_EMIT_SYMBOL, 185),
    (6, HUFFMAN_EMIT_SYMBOL, 185),
    (10, HUFFMAN_EMIT_SYMBOL, 185),
    (15, HUFFMAN_EMIT_SYMBOL, 185),
    (24, HUFFMAN_EMIT_SYMBOL, 185),
    (31, HUFFMAN_EMIT_SYMBOL, 185),
    (41, HUFFMAN_EMIT_SYMBOL, 185),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 185),
    (3, HUFFMAN_EMIT_SYMBOL, 186),
    (6, HUFFMAN_EMIT_SYMBOL, 186),
    (10, HUFFMAN_EMIT_SYMBOL, 186),
    (15, HUFFMAN_EMIT_SYMBOL, 186),
    (24, HUFFMAN_EMIT_SYMBOL, 186),
    (31, HUFFMAN_EMIT_SYMBOL, 186),
    (41, HUFFMAN_EMIT_SYMBOL, 186),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 186),

    # Node 139
    (2, HUFFMAN_EMIT_SYMBOL, 187),
    (9, HUFFMAN_EMIT_SYMBOL, 187),
    (23, HUFFMAN_EMIT_SYMBOL, 187),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 187),
    (2, HUFFMAN_EMIT_SYMBOL, 189),
    (9, HUFFMAN_EMIT_SYMBOL, 189),
    (23, HUFFMAN_EMIT_SYMBOL, 189),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 189),
    (2, HUFFMAN_EMIT_SYMBOL, 190),
    (9, HUFFMAN_EMIT_SYMBOL, 190),
    (23, HUFFMAN_EMIT_SYMBOL, 190),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 190),
    (2, HUFFMAN_EMIT_SYMBOL, 196),
    (9, HUFFMAN_EMIT_SYMBOL, 196),
    (23, HUFFMAN_EMIT_SYMBOL, 196),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 196),

    # Node 140
    (3, HUFFMAN_EMIT_SYMBOL, 187),
    (6, HUFFMAN_EMIT_SYMBOL, 187),
    (10, HUFFMAN_EMIT_SYMBOL, 187),
    (15, HUFFMAN_EMIT_SYMBOL, 187),
    (24, HUFFMAN_EMIT_SYMBOL, 187),
    (31, HUFFMAN_EMIT_SYMBOL, 187),
    (41, HUFFMAN_EMIT_SYMBOL, 187),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 187),
    (3, HUFFMAN_EMIT_SYMBOL, 189),
    (6, HUFFMAN_EMIT_SYMBOL, 189),
    (10, HUFFMAN_EMIT_SYMBOL, 189),
    (15, HUFFMAN_EMIT_SYMBOL, 189),
    (24, HUFFMAN_EMIT_SYMBOL, 189),
    (31, HUFFMAN_EMIT_SYMBOL, 189),
    (41, HUFFMAN_EMIT_SYMBOL, 189),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 189),

    # Node 141
    (3, HUFFMAN_EMIT_SYMBOL, 190),
    (6, HUFFMAN_EMIT_SYMBOL, 190),
    (10, HUFFMAN_EMIT_SYMBOL, 190),
    (15, HUFFMAN_EMIT_SYMBOL, 190),
    (24, HUFFMAN_EMIT_SYMBOL, 190),
    (31, HUFFMAN_EMIT_SYMBOL, 190),
    (41, HUFFMAN_EMIT_SYMBOL, 190),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 190),
    (3, HUFFMAN_EMIT_SYMBOL, 196),
    (6, HUFFMAN_EMIT_SYMBOL, 196),
    (10, HUFFMAN_EMIT_SYMBOL, 196),
    (15, HUFFMAN_EMIT_SYMBOL, 196),
    (24, HUFFMAN_EMIT_SYMBOL, 196),
    (31, HUFFMAN_EMIT_SYMBOL, 196),
    (41, HUFFMAN_EMIT_SYMBOL, 196),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 196),

    # Node 142
    (1, HUFFMAN_EMIT_SYMBOL, 198),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 198),
    (1, HUFFMAN_EMIT_SYMBOL, 228),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 228),
    (1, HUFFMAN_EMIT_SYMBOL, 232),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 232),
    (1, HUFFMAN_EMIT_SYMBOL, 233),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 233),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 1),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 135),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 137),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 138),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 139),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 140),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 141),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 143),

    # Node 143
    (2, HUFFMAN_EMIT_SYMBOL, 198),
    (9, HUFFMAN_EMIT_SYMBOL, 198),
    (23, HUFFMAN_EMIT_SYMBOL, 198),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 198),
    (2, HUFFMAN_EMIT_SYMBOL, 228),
    (9, HUFFMAN_EMIT_SYMBOL, 228),
    (23, HUFFMAN_EMIT_SYMBOL, 228),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 228),
    (2, HUFFMAN_EMIT_SYMBOL, 232),
    (9, HUFFMAN_EMIT_SYMBOL, 232),
    (23, HUFFMAN_EMIT_SYMBOL, 232),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 232),
    (2, HUFFMAN_EMIT_SYMBOL, 233),
    (9, HUFFMAN_EMIT_SYMBOL, 233),
    (23, HUFFMAN_EMIT_SYMBOL, 233),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 233),

    # Node 144
    (3, HUFFMAN_EMIT_SYMBOL, 198),
    (6, HUFFMAN_EMIT_SYMBOL, 198),
    (10, HUFFMAN_EMIT_SYMBOL, 198),
    (15, HUFFMAN_EMIT_SYMBOL, 198),
    (24, HUFFMAN_EMIT_SYMBOL, 198),
    (31, HUFFMAN_EMIT_SYMBOL, 198),
    (41, HUFFMAN_EMIT_SYMBOL, 198),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 198),
    (3, HUFFMAN_EMIT_SYMBOL, 228),
    (6, HUFFMAN_EMIT_SYMBOL, 228),
    (10, HUFFMAN_EMIT_SYMBOL, 228),
    (15, HUFFMAN_EMIT_SYMBOL, 228),
    (24, HUFFMAN_EMIT_SYMBOL, 228),
    (31, HUFFMAN_EMIT_SYMBOL, 228),
    (41, HUFFMAN_EMIT_SYMBOL, 228),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 228),

    # Node 145
    (3, HUFFMAN_EMIT_SYMBOL, 232),
    (6, HUFFMAN_EMIT_SYMBOL, 232),
    (10, HUFFMAN_EMIT_SYMBOL, 232),
    (15, HUFFMAN_EMIT_SYMBOL, 232),
    (24, HUFFMAN_EMIT_SYMBOL, 232),
    (31, HUFFMAN_EMIT_SYMBOL, 232),
    (41, HUFFMAN_EMIT_SYMBOL, 232),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 232),
    (3, HUFFMAN_EMIT_SYMBOL, 233),
    (6, HUFFMAN_EMIT_SYMBOL, 233),
    (10, HUFFMAN_EMIT_SYMBOL, 233),
    (15, HUFFMAN_EMIT_SYMBOL, 233),
    (24, HUFFMAN_EMIT_SYMBOL, 233),
    (31, HUFFMAN_EMIT_SYMBOL, 233),
    (41, HUFFMAN_EMIT_SYMBOL, 233),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 233),

    # Node 146
    (1, HUFFMAN_EMIT_SYMBOL, 1),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 1),
    (1, HUFFMAN_EMIT_SYMBOL, 135),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 135),
    (1, HUFFMAN_EMIT_SYMBOL, 137),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 137),
    (1, HUFFMAN_EMIT_SYMBOL, 138),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 138),
    (1, HUFFMAN_EMIT_SYMBOL, 139),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 139),
    (1, HUFFMAN_EMIT_SYMBOL, 140),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 140),
    (1, HUFFMAN_EMIT_SYMBOL, 141),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 141),
    (1, HUFFMAN_EMIT_SYMBOL, 143),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 143),

    # Node 147
    (2, HUFFMAN_EMIT_SYMBOL, 1),
    (9, HUFFMAN_EMIT_SYMBOL, 1),
    (23, HUFFMAN_EMIT_SYMBOL, 1),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 1),
    (2, HUFFMAN_EMIT_SYMBOL, 135),
    (9, HUFFMAN_EMIT_SYMBOL, 135),
    (23, HUFFMAN_EMIT_SYMBOL, 135),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 135),
    (2, HUFFMAN_EMIT_SYMBOL, 137),
    (9, HUFFMAN_EMIT_SYMBOL, 137),
    (23, HUFFMAN_EMIT_SYMBOL, 137),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 137),
    (2, HUFFMAN_EMIT_SYMBOL, 138),
    (9, HUFFMAN_EMIT_SYMBOL, 138),
    (23, HUFFMAN_EMIT_SYMBOL, 138),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 138),

    # Node 148
    (3, HUFFMAN_EMIT_SYMBOL, 1),
    (6, HUFFMAN_EMIT_SYMBOL, 1),
    (10, HUFFMAN_EMIT_SYMBOL, 1),
    (15, HUFFMAN_EMIT_SYMBOL, 1),
    (24, HUFFMAN_EMIT_SYMBOL, 1),
    (31, HUFFMAN_EMIT_SYMBOL, 1),
    (41, HUFFMAN_EMIT_SYMBOL, 1),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 1),
    (3, HUFFMAN_EMIT_SYMBOL, 135),
    (6, HUFFMAN_EMIT_SYMBOL, 135),
    (10, HUFFMAN_EMIT_SYMBOL, 135),
    (15, HUFFMAN_EMIT_SYMBOL, 135),
    (24, HUFFMAN_EMIT_SYMBOL, 135),
    (31, HUFFMAN_EMIT_SYMBOL, 135),
    (41, HUFFMAN_EMIT_SYMBOL, 135),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 135),

    # Node 149
    (3, HUFFMAN_EMIT_SYMBOL, 137),
    (6, HUFFMAN_EMIT_SYMBOL, 137),
    (10, HUFFMAN_EMIT_SYMBOL, 137),
    (15, HUFFMAN_EMIT_SYMBOL, 137),
    (24, HUFFMAN_EMIT_SYMBOL, 137),
    (31, HUFFMAN_EMIT_SYMBOL, 137),
    (41, HUFFMAN_EMIT_SYMBOL, 137),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 137),
    (3, HUFFMAN_EMIT_SYMBOL, 138),
    (6, HUFFMAN_EMIT_SYMBOL, 138),
    (10, HUFFMAN_EMIT_SYMBOL, 138),
    (15, HUFFMAN_EMIT_SYMBOL, 138),
    (24, HUFFMAN_EMIT_SYMBOL, 138),
    (31, HUFFMAN_EMIT_SYMBOL, 138),
    (41, HUFFMAN_EMIT_SYMBOL, 138),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 138),

    # Node 150
    (2, HUFFMAN_EMIT_SYMBOL, 139),
    (9, HUFFMAN_EMIT_SYMBOL, 139),
    (23, HUFFMAN_EMIT_SYMBOL, 139),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 139),
    (2, HUFFMAN_EMIT_SYMBOL, 140),
    (9, HUFFMAN_EMIT_SYMBOL, 140),
    (23, HUFFMAN_EMIT_SYMBOL, 140),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 140),
    (2, HUFFMAN_EMIT_SYMBOL, 141),
    (9, HUFFMAN_EMIT_SYMBOL, 141),
    (23, HUFFMAN_EMIT_SYMBOL, 141),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 141),
    (2, HUFFMAN_EMIT_SYMBOL, 143),
    (9, HUFFMAN_EMIT_SYMBOL, 143),
    (23, HUFFMAN_EMIT_SYMBOL, 143),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 143),

    # Node 151
    (3, HUFFMAN_EMIT_SYMBOL, 139),
    (6, HUFFMAN_EMIT_SYMBOL, 139),
    (10, HUFFMAN_EMIT_SYMBOL, 139),
    (15, HUFFMAN_EMIT_SYMBOL, 139),
    (24, HUFFMAN_EMIT_SYMBOL, 139),
    (31, HUFFMAN_EMIT_SYMBOL, 139),
    (41, HUFFMAN_EMIT_SYMBOL, 139),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 139),
    (3, HUFFMAN_EMIT_SYMBOL, 140),
    (6, HUFFMAN_EMIT_SYMBOL, 140),
    (10, HUFFMAN_EMIT_SYMBOL, 140),
    (15, HUFFMAN_EMIT_SYMBOL, 140),
    (24, HUFFMAN_EMIT_SYMBOL, 140),
    (31, HUFFMAN_EMIT_SYMBOL, 140),
    (41, HUFFMAN_EMIT_SYMBOL, 140),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 140),

    # Node 152
    (3, HUFFMAN_EMIT_SYMBOL, 141),
    (6, HUFFMAN_EMIT_SYMBOL, 141),
    (10, HUFFMAN_EMIT_SYMBOL, 141),
    (15, HUFFMAN_EMIT_SYMBOL, 141),
    (24, HUFFMAN_EMIT_SYMBOL, 141),
    (31, HUFFMAN_EMIT_SYMBOL, 141),
    (41, HUFFMAN_EMIT_SYMBOL, 141),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 141),
    (3, HUFFMAN_EMIT_SYMBOL, 143),
    (6, HUFFMAN_EMIT_SYMBOL, 143),
    (10, HUFFMAN_EMIT_SYMBOL, 143),
    (15, HUFFMAN_EMIT_SYMBOL, 143),
    (24, HUFFMAN_EMIT_SYMBOL, 143),
    (31, HUFFMAN_EMIT_SYMBOL, 143),
    (41, HUFFMAN_EMIT_SYMBOL, 143),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 143),

    # Node 153
    (157, 0, 0),
    (158, 0, 0),
    (160, 0, 0),
    (161, 0, 0),
    (164, 0, 0),
    (165, 0, 0),
    (167, 0, 0),
    (168, 0, 0),
    (172, 0, 0),
    (173, 0, 0),
    (175, 0, 0),
    (177, 0, 0),
    (182, 0, 0),
    (185, 0, 0),
    (191, 0, 0),
    (207, 0, 0),

    # Node 154
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 147),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 149),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 150),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 151),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 152),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 155),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 157),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 158),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 165),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 166),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 168),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 174),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 175),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 180),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 182),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 183),

    # Node 155
    (1, HUFFMAN_EMIT_SYMBOL, 147),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 147),
    (1, HUFFMAN_EMIT_SYMBOL, 149),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 149),
    (1, HUFFMAN_EMIT_SYMBOL, 150),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 150),
    (1, HUFFMAN_EMIT_SYMBOL, 151),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 151),
    (1, HUFFMAN_EMIT_SYMBOL, 152),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 152),
    (1, HUFFMAN_EMIT_SYMBOL, 155),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 155),
    (1, HUFFMAN_EMIT_SYMBOL, 157),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 157),
    (1, HUFFMAN_EMIT_SYMBOL, 158),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 158),

    # Node 156
    (2, HUFFMAN_EMIT_SYMBOL, 147),
    (9, HUFFMAN_EMIT_SYMBOL, 147),
    (23, HUFFMAN_EMIT_SYMBOL, 147),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 147),
    (2, HUFFMAN_EMIT_SYMBOL, 149),
    (9, HUFFMAN_EMIT_SYMBOL, 149),
    (23, HUFFMAN_EMIT_SYMBOL, 149),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 149),
    (2, HUFFMAN_EMIT_SYMBOL, 150),
    (9, HUFFMAN_EMIT_SYMBOL, 150),
    (23, HUFFMAN_EMIT_SYMBOL, 150),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 150),
    (2, HUFFMAN_EMIT_SYMBOL, 151),
    (9, HUFFMAN_EMIT_SYMBOL, 151),
    (23, HUFFMAN_EMIT_SYMBOL, 151),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 151),

    # Node 157
    (3, HUFFMAN_EMIT_SYMBOL, 147),
    (6, HUFFMAN_EMIT_SYMBOL, 147),
    (10, HUFFMAN_EMIT_SYMBOL, 147),
    (15, HUFFMAN_EMIT_SYMBOL, 147),
    (24, HUFFMAN_EMIT_SYMBOL, 147),
    (31, HUFFMAN_EMIT_SYMBOL, 147),
    (41, HUFFMAN_EMIT_SYMBOL, 147),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 147),
    (3, HUFFMAN_EMIT_SYMBOL, 149),
    (6, HUFFMAN_EMIT_SYMBOL, 149),
    (10, HUFFMAN_EMIT_SYMBOL, 149),
    (15, HUFFMAN_EMIT_SYMBOL, 149),
    (24, HUFFMAN_EMIT_SYMBOL, 149),
    (31, HUFFMAN_EMIT_SYMBOL, 149),
    (41, HUFFMAN_EMIT_SYMBOL, 149),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 149),

    # Node 158
    (3, HUFFMAN_EMIT_SYMBOL, 150),
    (6, HUFFMAN_EMIT_SYMBOL, 150),
    (10, HUFFMAN_EMIT_SYMBOL, 150),
    (15, HUFFMAN_EMIT_SYMBOL, 150),
    (24, HUFFMAN_EMIT_SYMBOL, 150),
    (31, HUFFMAN_EMIT_SYMBOL, 150),
    (41, HUFFMAN_EMIT_SYMBOL, 150),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 150),
    (3, HUFFMAN_EMIT_SYMBOL, 151),
    (6, HUFFMAN_EMIT_SYMBOL, 151),
    (10, HUFFMAN_EMIT_SYMBOL, 151),
    (15, HUFFMAN_EMIT_SYMBOL, 151),
    (24, HUFFMAN_EMIT_SYMBOL, 151),
    (31, HUFFMAN_EMIT_SYMBOL, 151),
    (41, HUFFMAN_EMIT_SYMBOL, 151),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 151),

    # Node 159
    (2, HUFFMAN_EMIT_SYMBOL, 152),
    (9, HUFFMAN_EMIT_SYMBOL, 152),
    (23, HUFFMAN_EMIT_SYMBOL, 152),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 152),
    (2, HUFFMAN_EMIT_SYMBOL, 155),
    (9, HUFFMAN_EMIT_SYMBOL, 155),
    (23, HUFFMAN_EMIT_SYMBOL, 155),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 155),
    (2, HUFFMAN_EMIT_SYMBOL, 157),
    (9, HUFFMAN_EMIT_SYMBOL, 157),
    (23, HUFFMAN_EMIT_SYMBOL, 157),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 157),
    (2, HUFFMAN_EMIT_SYMBOL, 158),
    (9, HUFFMAN_EMIT_SYMBOL, 158),
    (23, HUFFMAN_EMIT_SYMBOL, 158),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 158),

    # Node 160
    (3, HUFFMAN_EMIT_SYMBOL, 152),
    (6, HUFFMAN_EMIT_SYMBOL, 152),
    (10, HUFFMAN_EMIT_SYMBOL, 152),
    (15, HUFFMAN_EMIT_SYMBOL, 152),
    (24, HUFFMAN_EMIT_SYMBOL, 152),
    (31, HUFFMAN_EMIT_SYMBOL, 152),
    (41, HUFFMAN_EMIT_SYMBOL, 152),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 152),
    (3, HUFFMAN_EMIT_SYMBOL, 155),
    (6, HUFFMAN_EMIT_SYMBOL, 155),
    (10, HUFFMAN_EMIT_SYMBOL, 155),
    (15, HUFFMAN_EMIT_SYMBOL, 155),
    (24, HUFFMAN_EMIT_SYMBOL, 155),
    (31, HUFFMAN_EMIT_SYMBOL, 155),
    (41, HUFFMAN_EMIT_SYMBOL, 155),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 155),

    # Node 161
    (3, HUFFMAN_EMIT_SYMBOL, 157),
    (6, HUFFMAN_EMIT_SYMBOL, 157),
    (10, HUFFMAN_EMIT_SYMBOL, 157),
    (15, HUFFMAN_EMIT_SYMBOL, 157),
    (24, HUFFMAN_EMIT_SYMBOL, 157),
    (31, HUFFMAN_EMIT_SYMBOL, 157),
    (41, HUFFMAN_EMIT_SYMBOL, 157),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 157),
    (3, HUFFMAN_EMIT_SYMBOL, 158),
    (6, HUFFMAN_EMIT_SYMBOL, 158),
    (10, HUFFMAN_EMIT_SYMBOL, 158),
    (15, HUFFMAN_EMIT_SYMBOL, 158),
    (24, HUFFMAN_EMIT_SYMBOL, 158),
    (31, HUFFMAN_EMIT_SYMBOL, 158),
    (41, HUFFMAN_EMIT_SYMBOL, 158),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 158),

    # Node 162
    (1, HUFFMAN_EMIT_SYMBOL, 165),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 165),
    (1, HUFFMAN_EMIT_SYMBOL, 166),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 166),
    (1, HUFFMAN_EMIT_SYMBOL, 168),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 168),
    (1, HUFFMAN_EMIT_SYMBOL, 174),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 174),
    (1, HUFFMAN_EMIT_SYMBOL, 175),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 175),
    (1, HUFFMAN_EMIT_SYMBOL, 180),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 180),
    (1, HUFFMAN_EMIT_SYMBOL, 182),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 182),
    (1, HUFFMAN_EMIT_SYMBOL, 183),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 183),

    # Node 163
    (2, HUFFMAN_EMIT_SYMBOL, 165),
    (9, HUFFMAN_EMIT_SYMBOL, 165),
    (23, HUFFMAN_EMIT_SYMBOL, 165),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 165),
    (2, HUFFMAN_EMIT_SYMBOL, 166),
    (9, HUFFMAN_EMIT_SYMBOL, 166),
    (23, HUFFMAN_EMIT_SYMBOL, 166),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 166),
    (2, HUFFMAN_EMIT_SYMBOL, 168),
    (9, HUFFMAN_EMIT_SYMBOL, 168),
    (23, HUFFMAN_EMIT_SYMBOL, 168),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 168),
    (2, HUFFMAN_EMIT_SYMBOL, 174),
    (9, HUFFMAN_EMIT_SYMBOL, 174),
    (23, HUFFMAN_EMIT_SYMBOL, 174),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 174),

    # Node 164
    (3, HUFFMAN_EMIT_SYMBOL, 165),
    (6, HUFFMAN_EMIT_SYMBOL, 165),
    (10, HUFFMAN_EMIT_SYMBOL, 165),
    (15, HUFFMAN_EMIT_SYMBOL, 165),
    (24, HUFFMAN_EMIT_SYMBOL, 165),
    (31, HUFFMAN_EMIT_SYMBOL, 165),
    (41, HUFFMAN_EMIT_SYMBOL, 165),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 165),
    (3, HUFFMAN_EMIT_SYMBOL, 166),
    (6, HUFFMAN_EMIT_SYMBOL, 166),
    (10, HUFFMAN_EMIT_SYMBOL, 166),
    (15, HUFFMAN_EMIT_SYMBOL, 166),
    (24, HUFFMAN_EMIT_SYMBOL, 166),
    (31, HUFFMAN_EMIT_SYMBOL, 166),
    (41, HUFFMAN_EMIT_SYMBOL, 166),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 166),

    # Node 165
    (3, HUFFMAN_EMIT_SYMBOL, 168),
    (6, HUFFMAN_EMIT_SYMBOL, 168),
    (10, HUFFMAN_EMIT_SYMBOL, 168),
    (15, HUFFMAN_EMIT_SYMBOL, 168),
    (24, HUFFMAN_EMIT_SYMBOL, 168),
    (31, HUFFMAN_EMIT_SYMBOL, 168),
    (41, HUFFMAN_EMIT_SYMBOL, 168),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 168),
    (3, HUFFMAN_EMIT_SYMBOL, 174),
    (6, HUFFMAN_EMIT_SYMBOL, 174),
    (10, HUFFMAN_EMIT_SYMBOL, 174),
    (15, HUFFMAN_EMIT_SYMBOL, 174),
    (24, HUFFMAN_EMIT_SYMBOL, 174),
    (31, HUFFMAN_EMIT_SYMBOL, 174),
    (41, HUFFMAN_EMIT_SYMBOL, 174),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 174),

    # Node 166
    (2, HUFFMAN_EMIT_SYMBOL, 175),
    (9, HUFFMAN_EMIT_SYMBOL, 175),
    (23, HUFFMAN_EMIT_SYMBOL, 175),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 175),
    (2, HUFFMAN_EMIT_SYMBOL, 180),
    (9, HUFFMAN_EMIT_SYMBOL, 180),
    (23, HUFFMAN_EMIT_SYMBOL, 180),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 180),
    (2, HUFFMAN_EMIT_SYMBOL, 182),
    (9, HUFFMAN_EMIT_SYMBOL, 182),
    (23, HUFFMAN_EMIT_SYMBOL, 182),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 182),
    (2, HUFFMAN_EMIT_SYMBOL, 183),
    (9, HUFFMAN_EMIT_SYMBOL, 183),
    (23, HUFFMAN_EMIT_SYMBOL, 183),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 183),

    # Node 167
    (3, HUFFMAN_EMIT_SYMBOL, 175),
    (6, HUFFMAN_EMIT_SYMBOL, 175),
    (10, HUFFMAN_EMIT_SYMBOL, 175),
    (15, HUFFMAN_EMIT_SYMBOL, 175),
    (24, HUFFMAN_EMIT_SYMBOL, 175),
    (31, HUFFMAN_EMIT_SYMBOL, 175),
    (41, HUFFMAN_EMIT_SYMBOL, 175),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 175),
    (3, HUFFMAN_EMIT_SYMBOL, 180),
    (6, HUFFMAN_EMIT_SYMBOL, 180),
    (10, HUFFMAN_EMIT_SYMBOL, 180),
    (15, HUFFMAN_EMIT_SYMBOL, 180),
    (24, HUFFMAN_EMIT_SYMBOL, 180),
    (31, HUFFMAN_EMIT_SYMBOL, 180),
    (41, HUFFMAN_EMIT_SYMBOL, 180),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 180),

    # Node 168
    (3, HUFFMAN_EMIT_SYMBOL, 182),
    (6, HUFFMAN_EMIT_SYMBOL, 182),
    (10, HUFFMAN_EMIT_SYMBOL, 182),
    (15, HUFFMAN_EMIT_SYMBOL, 182),
    (24, HUFFMAN_EMIT_SYMBOL, 182),
    (31, HUFFMAN_EMIT_SYMBOL, 182),
    (41, HUFFMAN_EMIT_SYMBOL, 182),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 182),
    (3, HUFFMAN_EMIT_SYMBOL, 183),
    (6, HUFFMAN_EMIT_SYMBOL, 183),
    (10, HUFFMAN_EMIT_SYMBOL, 183),
    (15, HUFFMAN_EMIT_SYMBOL, 183),
    (24, HUFFMAN_EMIT_SYMBOL, 183),
    (31, HUFFMAN_EMIT_SYMBOL, 183),
    (41, HUFFMAN_EMIT_SYMBOL, 183),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 183),

    # Node 169
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 188),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 191),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 197),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 231),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 239),
    (176, 0, 0),
    (178, 0, 0),
    (179, 0, 0),
    (183, 0, 0),
    (184, 0, 0),
    (186, 0, 0),
    (187, 0, 0),
    (192, 0, 0),
    (199, 0, 0),
    (208, 0, 0),
    (223, 0, 0),

    # Node 170
    (1, HUFFMAN_EMIT_SYMBOL, 188),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 188),
    (1, HUFFMAN_EMIT_SYMBOL, 191),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 191),
    (1, HUFFMAN_EMIT_SYMBOL, 197),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 197),
    (1, HUFFMAN_EMIT_SYMBOL, 231),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 231),
    (1, HUFFMAN_EMIT_SYMBOL, 239),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 239),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 9),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 142),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 144),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 145),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 148),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 159),

    # Node 171
    (2, HUFFMAN_EMIT_SYMBOL, 188),
    (9, HUFFMAN_EMIT_SYMBOL, 188),
    (23, HUFFMAN_EMIT_SYMBOL, 188),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 188),
    (2, HUFFMAN_EMIT_SYMBOL, 191),
    (9, HUFFMAN_EMIT_SYMBOL, 191),
    (23, HUFFMAN_EMIT_SYMBOL, 191),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 191),
    (2, HUFFMAN_EMIT_SYMBOL, 197),
    (9, HUFFMAN_EMIT_SYMBOL, 197),
    (23, HUFFMAN_EMIT_SYMBOL, 197),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 197),
    (2, HUFFMAN_EMIT_SYMBOL, 231),
    (9, HUFFMAN_EMIT_SYMBOL, 231),
    (23, HUFFMAN_EMIT_SYMBOL, 231),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 231),

    # Node 172
    (3, HUFFMAN_EMIT_SYMBOL, 188),
    (6, HUFFMAN_EMIT_SYMBOL, 188),
    (10, HUFFMAN_EMIT_SYMBOL, 188),
    (15, HUFFMAN_EMIT_SYMBOL, 188),
    (24, HUFFMAN_EMIT_SYMBOL, 188),
    (31, HUFFMAN_EMIT_SYMBOL, 188),
    (41, HUFFMAN_EMIT_SYMBOL, 188),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 188),
    (3, HUFFMAN_EMIT_SYMBOL, 191),
    (6, HUFFMAN_EMIT_SYMBOL, 191),
    (10, HUFFMAN_EMIT_SYMBOL, 191),
    (15, HUFFMAN_EMIT_SYMBOL, 191),
    (24, HUFFMAN_EMIT_SYMBOL, 191),
    (31, HUFFMAN_EMIT_SYMBOL, 191),
    (41, HUFFMAN_EMIT_SYMBOL, 191),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 191),

    # Node 173
    (3, HUFFMAN_EMIT_SYMBOL, 197),
    (6, HUFFMAN_EMIT_SYMBOL, 197),
    (10, HUFFMAN_EMIT_SYMBOL, 197),
    (15, HUFFMAN_EMIT_SYMBOL, 197),
    (24, HUFFMAN_EMIT_SYMBOL, 197),
    (31, HUFFMAN_EMIT_SYMBOL, 197),
    (41, HUFFMAN_EMIT_SYMBOL, 197),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 197),
    (3, HUFFMAN_EMIT_SYMBOL, 231),
    (6, HUFFMAN_EMIT_SYMBOL, 231),
    (10, HUFFMAN_EMIT_SYMBOL, 231),
    (15, HUFFMAN_EMIT_SYMBOL, 231),
    (24, HUFFMAN_EMIT_SYMBOL, 231),
    (31, HUFFMAN_EMIT_SYMBOL, 231),
    (41, HUFFMAN_EMIT_SYMBOL, 231),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 231),

    # Node 174
    (2, HUFFMAN_EMIT_SYMBOL, 239),
    (9, HUFFMAN_EMIT_SYMBOL, 239),
    (23, HUFFMAN_EMIT_SYMBOL, 239),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 239),
    (1, HUFFMAN_EMIT_SYMBOL, 9),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 9),
    (1, HUFFMAN_EMIT_SYMBOL, 142),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 142),
    (1, HUFFMAN_EMIT_SYMBOL, 144),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 144),
    (1, HUFFMAN_EMIT_SYMBOL, 145),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 145),
    (1, HUFFMAN_EMIT_SYMBOL, 148),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 148),
    (1, HUFFMAN_EMIT_SYMBOL, 159),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 159),

    # Node 175
    (3, HUFFMAN_EMIT_SYMBOL, 239),
    (6, HUFFMAN_EMIT_SYMBOL, 239),
    (10, HUFFMAN_EMIT_SYMBOL, 239),
    (15, HUFFMAN_EMIT_SYMBOL, 239),
    (24, HUFFMAN_EMIT_SYMBOL, 239),
    (31, HUFFMAN_EMIT_SYMBOL, 239),
    (41, HUFFMAN_EMIT_SYMBOL, 239),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 239),
    (2, HUFFMAN_EMIT_SYMBOL, 9),
    (9, HUFFMAN_EMIT_SYMBOL, 9),
    (23, HUFFMAN_EMIT_SYMBOL, 9),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 9),
    (2, HUFFMAN_EMIT_SYMBOL, 142),
    (9, HUFFMAN_EMIT_SYMBOL, 142),
    (23, HUFFMAN_EMIT_SYMBOL, 142),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 142),

    # Node 176
    (3, HUFFMAN_EMIT_SYMBOL, 9),
    (6, HUFFMAN_EMIT_SYMBOL, 9),
    (10, HUFFMAN_EMIT_SYMBOL, 9),
    (15, HUFFMAN_EMIT_SYMBOL, 9),
    (24, HUFFMAN_EMIT_SYMBOL, 9),
    (31, HUFFMAN_EMIT_SYMBOL, 9),
    (41, HUFFMAN_EMIT_SYMBOL, 9),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 9),
    (3, HUFFMAN_EMIT_SYMBOL, 142),
    (6, HUFFMAN_EMIT_SYMBOL, 142),
    (10, HUFFMAN_EMIT_SYMBOL, 142),
    (15, HUFFMAN_EMIT_SYMBOL, 142),
    (24, HUFFMAN_EMIT_SYMBOL, 142),
    (31, HUFFMAN_EMIT_SYMBOL, 142),
    (41, HUFFMAN_EMIT_SYMBOL, 142),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 142),

    # Node 177
    (2, HUFFMAN_EMIT_SYMBOL, 144),
    (9, HUFFMAN_EMIT_SYMBOL, 144),
    (23, HUFFMAN_EMIT_SYMBOL, 144),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 144),
    (2, HUFFMAN_EMIT_SYMBOL, 145),
    (9, HUFFMAN_EMIT_SYMBOL, 145),
    (23, HUFFMAN_EMIT_SYMBOL, 145),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 145),
    (2, HUFFMAN_EMIT_SYMBOL, 148),
    (9, HUFFMAN_EMIT_SYMBOL, 148),
    (23, HUFFMAN_EMIT_SYMBOL, 148),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 148),
    (2, HUFFMAN_EMIT_SYMBOL, 159),
    (9, HUFFMAN_EMIT_SYMBOL, 159),
    (23, HUFFMAN_EMIT_SYMBOL, 159),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 159),

    # Node 178
    (3, HUFFMAN_EMIT_SYMBOL, 144),
    (6, HUFFMAN_EMIT_SYMBOL, 144),
    (10, HUFFMAN_EMIT_SYMBOL, 144),
    (15, HUFFMAN_EMIT_SYMBOL, 144),
    (24, HUFFMAN_EMIT_SYMBOL, 144),
    (31, HUFFMAN_EMIT_SYMBOL, 144),
    (41, HUFFMAN_EMIT_SYMBOL, 144),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 144),
    (3, HUFFMAN_EMIT_SYMBOL, 145),
    (6, HUFFMAN_EMIT_SYMBOL, 145),
    (10, HUFFMAN_EMIT_SYMBOL, 145),
    (15, HUFFMAN_EMIT_SYMBOL, 145),
    (24, HUFFMAN_EMIT_SYMBOL, 145),
    (31, HUFFMAN_EMIT_SYMBOL, 145),
    (41, HUFFMAN_EMIT_SYMBOL, 145),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 145),

    # Node 179
    (3, HUFFMAN_EMIT_SYMBOL, 148),
    (6, HUFFMAN_EMIT_SYMBOL, 148),
    (10, HUFFMAN_EMIT_SYMBOL, 148),
    (15, HUFFMAN_EMIT_SYMBOL, 148),
    (24, HUFFMAN_EMIT_SYMBOL, 148),
    (31, HUFFMAN_EMIT_SYMBOL, 148),
    (41, HUFFMAN_EMIT_SYMBOL, 148),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 148),
    (3, HUFFMAN_EMIT_SYMBOL, 159),
    (6, HUFFMAN_EMIT_SYMBOL, 159),
    (10, HUFFMAN_EMIT_SYMBOL, 159),
    (15, HUFFMAN_EMIT_SYMBOL, 159),
    (24, HUFFMAN_EMIT_SYMBOL, 159),
    (31, HUFFMAN_EMIT_SYMBOL, 159),
    (41, HUFFMAN_EMIT_SYMBOL, 159),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 159),

    # Node 180
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 171),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 206),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 215),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 225),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 236),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 237),
    (188, 0, 0),
    (189, 0, 0),
    (193, 0, 0),
    (196, 0, 0),
    (200, 0, 0),
    (203, 0, 0),
    (209, 0, 0),
    (216, 0, 0),
    (224, 0, 0),
    (238, 0, 0),

    # Node 181
    (1, HUFFMAN_EMIT_SYMBOL, 171),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 171),
    (1, HUFFMAN_EMIT_SYMBOL, 206),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 206),
    (1, HUFFMAN_EMIT_SYMBOL, 215),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 215),
    (1, HUFFMAN_EMIT_SYMBOL, 225),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 225),
    (1, HUFFMAN_EMIT_SYMBOL, 236),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 236),
    (1, HUFFMAN_EMIT_SYMBOL, 237),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 237),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 199),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 207),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 234),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 235),

    # Node 182
    (2, HUFFMAN_EMIT_SYMBOL, 171),
    (9, HUFFMAN_EMIT_SYMBOL, 171),
    (23, HUFFMAN_EMIT_SYMBOL, 171),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 171),
    (2, HUFFMAN_EMIT_SYMBOL, 206),
    (9, HUFFMAN_EMIT_SYMBOL, 206),
    (23, HUFFMAN_EMIT_SYMBOL, 206),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 206),
    (2, HUFFMAN_EMIT_SYMBOL, 215),
    (9, HUFFMAN_EMIT_SYMBOL, 215),
    (23, HUFFMAN_EMIT_SYMBOL, 215),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 215),
    (2, HUFFMAN_EMIT_SYMBOL, 225),
    (9, HUFFMAN_EMIT_SYMBOL, 225),
    (23, HUFFMAN_EMIT_SYMBOL, 225),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 225),

    # Node 183
    (3, HUFFMAN_EMIT_SYMBOL, 171),
    (6, HUFFMAN_EMIT_SYMBOL, 171),
    (10, HUFFMAN_EMIT_SYMBOL, 171),
    (15, HUFFMAN_EMIT_SYMBOL, 171),
    (24, HUFFMAN_EMIT_SYMBOL, 171),
    (31, HUFFMAN_EMIT_SYMBOL, 171),
    (41, HUFFMAN_EMIT_SYMBOL, 171),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 171),
    (3, HUFFMAN_EMIT_SYMBOL, 206),
    (6, HUFFMAN_EMIT_SYMBOL, 206),
    (10, HUFFMAN_EMIT_SYMBOL, 206),
    (15, HUFFMAN_EMIT_SYMBOL, 206),
    (24, HUFFMAN_EMIT_SYMBOL, 206),
    (31, HUFFMAN_EMIT_SYMBOL, 206),
    (41, HUFFMAN_EMIT_SYMBOL, 206),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 206),

    # Node 184
    (3, HUFFMAN_EMIT_SYMBOL, 215),
    (6, HUFFMAN_EMIT_SYMBOL, 215),
    (10, HUFFMAN_EMIT_SYMBOL, 215),
    (15, HUFFMAN_EMIT_SYMBOL, 215),
    (24, HUFFMAN_EMIT_SYMBOL, 215),
    (31, HUFFMAN_EMIT_SYMBOL, 215),
    (41, HUFFMAN_EMIT_SYMBOL, 215),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 215),
    (3, HUFFMAN_EMIT_SYMBOL, 225),
    (6, HUFFMAN_EMIT_SYMBOL, 225),
    (10, HUFFMAN_EMIT_SYMBOL, 225),
    (15, HUFFMAN_EMIT_SYMBOL, 225),
    (24, HUFFMAN_EMIT_SYMBOL, 225),
    (31, HUFFMAN_EMIT_SYMBOL, 225),
    (41, HUFFMAN_EMIT_SYMBOL, 225),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 225),

    # Node 185
    (2, HUFFMAN_EMIT_SYMBOL, 236),
    (9, HUFFMAN_EMIT_SYMBOL, 236),
    (23, HUFFMAN_EMIT_SYMBOL, 236),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 236),
    (2, HUFFMAN_EMIT_SYMBOL, 237),
    (9, HUFFMAN_EMIT_SYMBOL, 237),
    (23, HUFFMAN_EMIT_SYMBOL, 237),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 237),
    (1, HUFFMAN_EMIT_SYMBOL, 199),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 199),
    (1, HUFFMAN_EMIT_SYMBOL, 207),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 207),
    (1, HUFFMAN_EMIT_SYMBOL, 234),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 234),
    (1, HUFFMAN_EMIT_SYMBOL, 235),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 235),

    # Node 186
    (3, HUFFMAN_EMIT_SYMBOL, 236),
    (6, HUFFMAN_EMIT_SYMBOL, 236),
    (10, HUFFMAN_EMIT_SYMBOL, 236),
    (15, HUFFMAN_EMIT_SYMBOL, 236),
    (24, HUFFMAN_EMIT_SYMBOL, 236),
    (31, HUFFMAN_EMIT_SYMBOL, 236),
    (41, HUFFMAN_EMIT_SYMBOL, 236),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 236),
    (3, HUFFMAN_EMIT_SYMBOL, 237),
    (6, HUFFMAN_EMIT_SYMBOL, 237),
    (10, HUFFMAN_EMIT_SYMBOL, 237),
    (15, HUFFMAN_EMIT_SYMBOL, 237),
    (24, HUFFMAN_EMIT_SYMBOL, 237),
    (31, HUFFMAN_EMIT_SYMBOL, 237),
    (41, HUFFMAN_EMIT_SYMBOL, 237),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 237),

    # Node 187
    (2, HUFFMAN_EMIT_SYMBOL, 199),
    (9, HUFFMAN_EMIT_SYMBOL, 199),
    (23, HUFFMAN_EMIT_SYMBOL, 199),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 199),
    (2, HUFFMAN_EMIT_SYMBOL, 207),
    (9, HUFFMAN_EMIT_SYMBOL, 207),
    (23, HUFFMAN_EMIT_SYMBOL, 207),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 207),
    (2, HUFFMAN_EMIT_SYMBOL, 234),
    (9, HUFFMAN_EMIT_SYMBOL, 234),
    (23, HUFFMAN_EMIT_SYMBOL, 234),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 234),
    (2, HUFFMAN_EMIT_SYMBOL, 235),
    (9, HUFFMAN_EMIT_SYMBOL, 235),
    (23, HUFFMAN_EMIT_SYMBOL, 235),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 235),

    # Node 188
    (3, HUFFMAN_EMIT_SYMBOL, 199),
    (6, HUFFMAN_EMIT_SYMBOL, 199),
    (10, HUFFMAN_EMIT_SYMBOL, 199),
    (15, HUFFMAN_EMIT_SYMBOL, 199),
    (24, HUFFMAN_EMIT_SYMBOL, 199),
    (31, HUFFMAN_EMIT_SYMBOL, 199),
    (41, HUFFMAN_EMIT_SYMBOL, 199),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 199),
    (3, HUFFMAN_EMIT_SYMBOL, 207),
    (6, HUFFMAN_EMIT_SYMBOL, 207),
    (10, HUFFMAN_EMIT_SYMBOL, 207),
    (15, HUFFMAN_EMIT_SYMBOL, 207),
    (24, HUFFMAN_EMIT_SYMBOL, 207),
    (31, HUFFMAN_EMIT_SYMBOL, 207),
    (41, HUFFMAN_EMIT_SYMBOL, 207),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 207),

    # Node 189
    (3, HUFFMAN_EMIT_SYMBOL, 234),
    (6, HUFFMAN_EMIT_SYMBOL, 234),
    (10, HUFFMAN_EMIT_SYMBOL, 234),
    (15, HUFFMAN_EMIT_SYMBOL, 234),
    (24, HUFFMAN_EMIT_SYMBOL, 234),
    (31, HUFFMAN_EMIT_SYMBOL, 234),
    (41, HUFFMAN_EMIT_SYMBOL, 234),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 234),
    (3, HUFFMAN_EMIT_SYMBOL, 235),
    (6, HUFFMAN_EMIT_SYMBOL, 235),
    (10, HUFFMAN_EMIT_SYMBOL, 235),
    (15, HUFFMAN_EMIT_SYMBOL, 235),
    (24, HUFFMAN_EMIT_SYMBOL, 235),
    (31, HUFFMAN_EMIT_SYMBOL, 235),
    (41, HUFFMAN_EMIT_SYMBOL, 235),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 235),

    # Node 190
    (194, 0, 0),
    (195, 0, 0),
    (197, 0, 0),
    (198, 0, 0),
    (201, 0, 0),
    (202, 0, 0),
    (204, 0, 0),
    (205, 0, 0),
    (210, 0, 0),
    (213, 0, 0),
    (217, 0, 0),
    (220, 0, 0),
    (225, 0, 0),
    (231, 0, 0),
    (239, 0, 0),
    (246, 0, 0),

    # Node 191
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 192),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 193),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 200),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 201),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 202),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 205),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 210),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 213),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 218),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 219),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 238),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 240),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 242),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 243),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 255),
    (206, 0, 0),

    # Node 192
    (1, HUFFMAN_EMIT_SYMBOL, 192),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 192),
    (1, HUFFMAN_EMIT_SYMBOL, 193),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 193),
    (1, HUFFMAN_EMIT_SYMBOL, 200),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 200),
    (1, HUFFMAN_EMIT_SYMBOL, 201),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 201),
    (1, HUFFMAN_EMIT_SYMBOL, 202),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 202),
    (1, HUFFMAN_EMIT_SYMBOL, 205),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 205),
    (1, HUFFMAN_EMIT_SYMBOL, 210),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 210),
    (1, HUFFMAN_EMIT_SYMBOL, 213),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 213),

    # Node 193
    (2, HUFFMAN_EMIT_SYMBOL, 192),
    (9, HUFFMAN_EMIT_SYMBOL, 192),
    (23, HUFFMAN_EMIT_SYMBOL, 192),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 192),
    (2, HUFFMAN_EMIT_SYMBOL, 193),
    (9, HUFFMAN_EMIT_SYMBOL, 193),
    (23, HUFFMAN_EMIT_SYMBOL, 193),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 193),
    (2, HUFFMAN_EMIT_SYMBOL, 200),
    (9, HUFFMAN_EMIT_SYMBOL, 200),
    (23, HUFFMAN_EMIT_SYMBOL, 200),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 200),
    (2, HUFFMAN_EMIT_SYMBOL, 201),
    (9, HUFFMAN_EMIT_SYMBOL, 201),
    (23, HUFFMAN_EMIT_SYMBOL, 201),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 201),

    # Node 194
    (3, HUFFMAN_EMIT_SYMBOL, 192),
    (6, HUFFMAN_EMIT_SYMBOL, 192),
    (10, HUFFMAN_EMIT_SYMBOL, 192),
    (15, HUFFMAN_EMIT_SYMBOL, 192),
    (24, HUFFMAN_EMIT_SYMBOL, 192),
    (31, HUFFMAN_EMIT_SYMBOL, 192),
    (41, HUFFMAN_EMIT_SYMBOL, 192),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 192),
    (3, HUFFMAN_EMIT_SYMBOL, 193),
    (6, HUFFMAN_EMIT_SYMBOL, 193),
    (10, HUFFMAN_EMIT_SYMBOL, 193),
    (15, HUFFMAN_EMIT_SYMBOL, 193),
    (24, HUFFMAN_EMIT_SYMBOL, 193),
    (31, HUFFMAN_EMIT_SYMBOL, 193),
    (41, HUFFMAN_EMIT_SYMBOL, 193),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 193),

    # Node 195
    (3, HUFFMAN_EMIT_SYMBOL, 200),
    (6, HUFFMAN_EMIT_SYMBOL, 200),
    (10, HUFFMAN_EMIT_SYMBOL, 200),
    (15, HUFFMAN_EMIT_SYMBOL, 200),
    (24, HUFFMAN_EMIT_SYMBOL, 200),
    (31, HUFFMAN_EMIT_SYMBOL, 200),
    (41, HUFFMAN_EMIT_SYMBOL, 200),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 200),
    (3, HUFFMAN_EMIT_SYMBOL, 201),
    (6, HUFFMAN_EMIT_SYMBOL, 201),
    (10, HUFFMAN_EMIT_SYMBOL, 201),
    (15, HUFFMAN_EMIT_SYMBOL, 201),
    (24, HUFFMAN_EMIT_SYMBOL, 201),
    (31, HUFFMAN_EMIT_SYMBOL, 201),
    (41, HUFFMAN_EMIT_SYMBOL, 201),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 201),

    # Node 196
    (2, HUFFMAN_EMIT_SYMBOL, 202),
    (9, HUFFMAN_EMIT_SYMBOL, 202),
    (23, HUFFMAN_EMIT_SYMBOL, 202),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 202),
    (2, HUFFMAN_EMIT_SYMBOL, 205),
    (9, HUFFMAN_EMIT_SYMBOL, 205),
    (23, HUFFMAN_EMIT_SYMBOL, 205),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 205),
    (2, HUFFMAN_EMIT_SYMBOL, 210),
    (9, HUFFMAN_EMIT_SYMBOL, 210),
    (23, HUFFMAN_EMIT_SYMBOL, 210),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 210),
    (2, HUFFMAN_EMIT_SYMBOL, 213),
    (9, HUFFMAN_EMIT_SYMBOL, 213),
    (23, HUFFMAN_EMIT_SYMBOL, 213),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 213),

    # Node 197
    (3, HUFFMAN_EMIT_SYMBOL, 202),
    (6, HUFFMAN_EMIT_SYMBOL, 202),
    (10, HUFFMAN_EMIT_SYMBOL, 202),
    (15, HUFFMAN_EMIT_SYMBOL, 202),
    (24, HUFFMAN_EMIT_SYMBOL, 202),
    (31, HUFFMAN_EMIT_SYMBOL, 202),
    (41, HUFFMAN_EMIT_SYMBOL, 202),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 202),
    (3, HUFFMAN_EMIT_SYMBOL, 205),
    (6, HUFFMAN_EMIT_SYMBOL, 205),
    (10, HUFFMAN_EMIT_SYMBOL, 205),
    (15, HUFFMAN_EMIT_SYMBOL, 205),
    (24, HUFFMAN_EMIT_SYMBOL, 205),
    (31, HUFFMAN_EMIT_SYMBOL, 205),
    (41, HUFFMAN_EMIT_SYMBOL, 205),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 205),

    # Node 198
    (3, HUFFMAN_EMIT_SYMBOL, 210),
    (6, HUFFMAN_EMIT_SYMBOL, 210),
    (10, HUFFMAN_EMIT_SYMBOL, 210),
    (15, HUFFMAN_EMIT_SYMBOL, 210),
    (24, HUFFMAN_EMIT_SYMBOL, 210),
    (31, HUFFMAN_EMIT_SYMBOL, 210),
    (41, HUFFMAN_EMIT_SYMBOL, 210),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 210),
    (3, HUFFMAN_EMIT_SYMBOL, 213),
    (6, HUFFMAN_EMIT_SYMBOL, 213),
    (10, HUFFMAN_EMIT_SYMBOL, 213),
    (15, HUFFMAN_EMIT_SYMBOL, 213),
    (24, HUFFMAN_EMIT_SYMBOL, 213),
    (31, HUFFMAN_EMIT_SYMBOL, 213),
    (41, HUFFMAN_EMIT_SYMBOL, 213),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 213),

    # Node 199
    (1, HUFFMAN_EMIT_SYMBOL, 218),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 218),
    (1, HUFFMAN_EMIT_SYMBOL, 219),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 219),
    (1, HUFFMAN_EMIT_SYMBOL, 238),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 238),
    (1, HUFFMAN_EMIT_SYMBOL, 240),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 240),
    (1, HUFFMAN_EMIT_SYMBOL, 242),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 242),
    (1, HUFFMAN_EMIT_SYMBOL, 243),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 243),
    (1, HUFFMAN_EMIT_SYMBOL, 255),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 255),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 203),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 204),

    # Node 200
    (2, HUFFMAN_EMIT_SYMBOL, 218),
    (9, HUFFMAN_EMIT_SYMBOL, 218),
    (23, HUFFMAN_EMIT_SYMBOL, 218),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 218),
    (2, HUFFMAN_EMIT_SYMBOL, 219),
    (9, HUFFMAN_EMIT_SYMBOL, 219),
    (23, HUFFMAN_EMIT_SYMBOL, 219),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 219),
    (2, HUFFMAN_EMIT_SYMBOL, 238),
    (9, HUFFMAN_EMIT_SYMBOL, 238),
    (23, HUFFMAN_EMIT_SYMBOL, 238),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 238),
    (2, HUFFMAN_EMIT_SYMBOL, 240),
    (9, HUFFMAN_EMIT_SYMBOL, 240),
    (23, HUFFMAN_EMIT_SYMBOL, 240),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 240),

    # Node 201
    (3, HUFFMAN_EMIT_SYMBOL, 218),
    (6, HUFFMAN_EMIT_SYMBOL, 218),
    (10, HUFFMAN_EMIT_SYMBOL, 218),
    (15, HUFFMAN_EMIT_SYMBOL, 218),
    (24, HUFFMAN_EMIT_SYMBOL, 218),
    (31, HUFFMAN_EMIT_SYMBOL, 218),
    (41, HUFFMAN_EMIT_SYMBOL, 218),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 218),
    (3, HUFFMAN_EMIT_SYMBOL, 219),
    (6, HUFFMAN_EMIT_SYMBOL, 219),
    (10, HUFFMAN_EMIT_SYMBOL, 219),
    (15, HUFFMAN_EMIT_SYMBOL, 219),
    (24, HUFFMAN_EMIT_SYMBOL, 219),
    (31, HUFFMAN_EMIT_SYMBOL, 219),
    (41, HUFFMAN_EMIT_SYMBOL, 219),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 219),

    # Node 202
    (3, HUFFMAN_EMIT_SYMBOL, 238),
    (6, HUFFMAN_EMIT_SYMBOL, 238),
    (10, HUFFMAN_EMIT_SYMBOL, 238),
    (15, HUFFMAN_EMIT_SYMBOL, 238),
    (24, HUFFMAN_EMIT_SYMBOL, 238),
    (31, HUFFMAN_EMIT_SYMBOL, 238),
    (41, HUFFMAN_EMIT_SYMBOL, 238),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 238),
    (3, HUFFMAN_EMIT_SYMBOL, 240),
    (6, HUFFMAN_EMIT_SYMBOL, 240),
    (10, HUFFMAN_EMIT_SYMBOL, 240),
    (15, HUFFMAN_EMIT_SYMBOL, 240),
    (24, HUFFMAN_EMIT_SYMBOL, 240),
    (31, HUFFMAN_EMIT_SYMBOL, 240),
    (41, HUFFMAN_EMIT_SYMBOL, 240),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 240),

    # Node 203
    (2, HUFFMAN_EMIT_SYMBOL, 242),
    (9, HUFFMAN_EMIT_SYMBOL, 242),
    (23, HUFFMAN_EMIT_SYMBOL, 242),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 242),
    (2, HUFFMAN_EMIT_SYMBOL, 243),
    (9, HUFFMAN_EMIT_SYMBOL, 243),
    (23, HUFFMAN_EMIT_SYMBOL, 243),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 243),
    (2, HUFFMAN_EMIT_SYMBOL, 255),
    (9, HUFFMAN_EMIT_SYMBOL, 255),
    (23, HUFFMAN_EMIT_SYMBOL, 255),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 255),
    (1, HUFFMAN_EMIT_SYMBOL, 203),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 203),
    (1, HUFFMAN_EMIT_SYMBOL, 204),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 204),

    # Node 204
    (3, HUFFMAN_EMIT_SYMBOL, 242),
    (6, HUFFMAN_EMIT_SYMBOL, 242),
    (10, HUFFMAN_EMIT_SYMBOL, 242),
    (15, HUFFMAN_EMIT_SYMBOL, 242),
    (24, HUFFMAN_EMIT_SYMBOL, 242),
    (31, HUFFMAN_EMIT_SYMBOL, 242),
    (41, HUFFMAN_EMIT_SYMBOL, 242),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 242),
    (3, HUFFMAN_EMIT_SYMBOL, 243),
    (6, HUFFMAN_EMIT_SYMBOL, 243),
    (10, HUFFMAN_EMIT_SYMBOL, 243),
    (15, HUFFMAN_EMIT_SYMBOL, 243),
    (24, HUFFMAN_EMIT_SYMBOL, 243),
    (31, HUFFMAN_EMIT_SYMBOL, 243),
    (41, HUFFMAN_EMIT_SYMBOL, 243),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 243),

    # Node 205
    (3, HUFFMAN_EMIT_SYMBOL, 255),
    (6, HUFFMAN_EMIT_SYMBOL, 255),
    (10, HUFFMAN_EMIT_SYMBOL, 255),
    (15, HUFFMAN_EMIT_SYMBOL, 255),
    (24, HUFFMAN_EMIT_SYMBOL, 255),
    (31, HUFFMAN_EMIT_SYMBOL, 255),
    (41, HUFFMAN_EMIT_SYMBOL, 255),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 255),
    (2, HUFFMAN_EMIT_SYMBOL, 203),
    (9, HUFFMAN_EMIT_SYMBOL, 203),
    (23, HUFFMAN_EMIT_SYMBOL, 203),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 203),
    (2, HUFFMAN_EMIT_SYMBOL, 204),
    (9, HUFFMAN_EMIT_SYMBOL, 204),
    (23, HUFFMAN_EMIT_SYMBOL, 204),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 204),

    # Node 206
    (3, HUFFMAN_EMIT_SYMBOL, 203),
    (6, HUFFMAN_EMIT_SYMBOL, 203),
    (10, HUFFMAN_EMIT_SYMBOL, 203),
    (15, HUFFMAN_EMIT_SYMBOL, 203),
    (24, HUFFMAN_EMIT_SYMBOL, 203),
    (31, HUFFMAN_EMIT_SYMBOL, 203),
    (41, HUFFMAN_EMIT_SYMBOL, 203),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 203),
    (3, HUFFMAN_EMIT_SYMBOL, 204),
    (6, HUFFMAN_EMIT_SYMBOL, 204),
    (10, HUFFMAN_EMIT_SYMBOL, 204),
    (15, HUFFMAN_EMIT_SYMBOL, 204),
    (24, HUFFMAN_EMIT_SYMBOL, 204),
    (31, HUFFMAN_EMIT_SYMBOL, 204),
    (41, HUFFMAN_EMIT_SYMBOL, 204),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 204),

    # Node 207
    (211, 0, 0),
    (212, 0, 0),
    (214, 0, 0),
    (215, 0, 0),
    (218, 0, 0),
    (219, 0, 0),
    (221, 0, 0),
    (222, 0, 0),
    (226, 0, 0),
    (228, 0, 0),
    (232, 0, 0),
    (235, 0, 0),
    (240, 0, 0),
    (243, 0, 0),
    (247, 0, 0),
    (250, 0, 0),

    # Node 208
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 211),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 212),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 214),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 221),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 222),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 223),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 241),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 244),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 245),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 246),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 247),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 248),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 250),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 251),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 252),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 253),

    # Node 209
    (1, HUFFMAN_EMIT_SYMBOL, 211),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 211),
    (1, HUFFMAN_EMIT_SYMBOL, 212),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 212),
    (1, HUFFMAN_EMIT_SYMBOL, 214),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 214),
    (1, HUFFMAN_EMIT_SYMBOL, 221),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 221),
    (1, HUFFMAN_EMIT_SYMBOL, 222),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 222),
    (1, HUFFMAN_EMIT_SYMBOL, 223),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 223),
    (1, HUFFMAN_EMIT_SYMBOL, 241),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 241),
    (1, HUFFMAN_EMIT_SYMBOL, 244),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 244),

    # Node 210
    (2, HUFFMAN_EMIT_SYMBOL, 211),
    (9, HUFFMAN_EMIT_SYMBOL, 211),
    (23, HUFFMAN_EMIT_SYMBOL, 211),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 211),
    (2, HUFFMAN_EMIT_SYMBOL, 212),
    (9, HUFFMAN_EMIT_SYMBOL, 212),
    (23, HUFFMAN_EMIT_SYMBOL, 212),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 212),
    (2, HUFFMAN_EMIT_SYMBOL, 214),
    (9, HUFFMAN_EMIT_SYMBOL, 214),
    (23, HUFFMAN_EMIT_SYMBOL, 214),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 214),
    (2, HUFFMAN_EMIT_SYMBOL, 221),
    (9, HUFFMAN_EMIT_SYMBOL, 221),
    (23, HUFFMAN_EMIT_SYMBOL, 221),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 221),

    # Node 211
    (3, HUFFMAN_EMIT_SYMBOL, 211),
    (6, HUFFMAN_EMIT_SYMBOL, 211),
    (10, HUFFMAN_EMIT_SYMBOL, 211),
    (15, HUFFMAN_EMIT_SYMBOL, 211),
    (24, HUFFMAN_EMIT_SYMBOL, 211),
    (31, HUFFMAN_EMIT_SYMBOL, 211),
    (41, HUFFMAN_EMIT_SYMBOL, 211),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 211),
    (3, HUFFMAN_EMIT_SYMBOL, 212),
    (6, HUFFMAN_EMIT_SYMBOL, 212),
    (10, HUFFMAN_EMIT_SYMBOL, 212),
    (15, HUFFMAN_EMIT_SYMBOL, 212),
    (24, HUFFMAN_EMIT_SYMBOL, 212),
    (31, HUFFMAN_EMIT_SYMBOL, 212),
    (41, HUFFMAN_EMIT_SYMBOL, 212),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 212),

    # Node 212
    (3, HUFFMAN_EMIT_SYMBOL, 214),
    (6, HUFFMAN_EMIT_SYMBOL, 214),
    (10, HUFFMAN_EMIT_SYMBOL, 214),
    (15, HUFFMAN_EMIT_SYMBOL, 214),
    (24, HUFFMAN_EMIT_SYMBOL, 214),
    (31, HUFFMAN_EMIT_SYMBOL, 214),
    (41, HUFFMAN_EMIT_SYMBOL, 214),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 214),
    (3, HUFFMAN_EMIT_SYMBOL, 221),
    (6, HUFFMAN_EMIT_SYMBOL, 221),
    (10, HUFFMAN_EMIT_SYMBOL, 221),
    (15, HUFFMAN_EMIT_SYMBOL, 221),
    (24, HUFFMAN_EMIT_SYMBOL, 221),
    (31, HUFFMAN_EMIT_SYMBOL, 221),
    (41, HUFFMAN_EMIT_SYMBOL, 221),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 221),

    # Node 213
    (2, HUFFMAN_EMIT_SYMBOL, 222),
    (9, HUFFMAN_EMIT_SYMBOL, 222),
    (23, HUFFMAN_EMIT_SYMBOL, 222),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 222),
    (2, HUFFMAN_EMIT_SYMBOL, 223),
    (9, HUFFMAN_EMIT_SYMBOL, 223),
    (23, HUFFMAN_EMIT_SYMBOL, 223),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 223),
    (2, HUFFMAN_EMIT_SYMBOL, 241),
    (9, HUFFMAN_EMIT_SYMBOL, 241),
    (23, HUFFMAN_EMIT_SYMBOL, 241),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 241),
    (2, HUFFMAN_EMIT_SYMBOL, 244),
    (9, HUFFMAN_EMIT_SYMBOL, 244),
    (23, HUFFMAN_EMIT_SYMBOL, 244),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 244),

    # Node 214
    (3, HUFFMAN_EMIT_SYMBOL, 222),
    (6, HUFFMAN_EMIT_SYMBOL, 222),
    (10, HUFFMAN_EMIT_SYMBOL, 222),
    (15, HUFFMAN_EMIT_SYMBOL, 222),
    (24, HUFFMAN_EMIT_SYMBOL, 222),
    (31, HUFFMAN_EMIT_SYMBOL, 222),
    (41, HUFFMAN_EMIT_SYMBOL, 222),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 222),
    (3, HUFFMAN_EMIT_SYMBOL, 223),
    (6, HUFFMAN_EMIT_SYMBOL, 223),
    (10, HUFFMAN_EMIT_SYMBOL, 223),
    (15, HUFFMAN_EMIT_SYMBOL, 223),
    (24, HUFFMAN_EMIT_SYMBOL, 223),
    (31, HUFFMAN_EMIT_SYMBOL, 223),
    (41, HUFFMAN_EMIT_SYMBOL, 223),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 223),

    # Node 215
    (3, HUFFMAN_EMIT_SYMBOL, 241),
    (6, HUFFMAN_EMIT_SYMBOL, 241),
    (10, HUFFMAN_EMIT_SYMBOL, 241),
    (15, HUFFMAN_EMIT_SYMBOL, 241),
    (24, HUFFMAN_EMIT_SYMBOL, 241),
    (31, HUFFMAN_EMIT_SYMBOL, 241),
    (41, HUFFMAN_EMIT_SYMBOL, 241),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 241),
    (3, HUFFMAN_EMIT_SYMBOL, 244),
    (6, HUFFMAN_EMIT_SYMBOL, 244),
    (10, HUFFMAN_EMIT_SYMBOL, 244),
    (15, HUFFMAN_EMIT_SYMBOL, 244),
    (24, HUFFMAN_EMIT_SYMBOL, 244),
    (31, HUFFMAN_EMIT_SYMBOL, 244),
    (41, HUFFMAN_EMIT_SYMBOL, 244),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 244),

    # Node 216
    (1, HUFFMAN_EMIT_SYMBOL, 245),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 245),
    (1, HUFFMAN_EMIT_SYMBOL, 246),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 246),
    (1, HUFFMAN_EMIT_SYMBOL, 247),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 247),
    (1, HUFFMAN_EMIT_SYMBOL, 248),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 248),
    (1, HUFFMAN_EMIT_SYMBOL, 250),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 250),
    (1, HUFFMAN_EMIT_SYMBOL, 251),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 251),
    (1, HUFFMAN_EMIT_SYMBOL, 252),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 252),
    (1, HUFFMAN_EMIT_SYMBOL, 253),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 253),

    # Node 217
    (2, HUFFMAN_EMIT_SYMBOL, 245),
    (9, HUFFMAN_EMIT_SYMBOL, 245),
    (23, HUFFMAN_EMIT_SYMBOL, 245),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 245),
    (2, HUFFMAN_EMIT_SYMBOL, 246),
    (9, HUFFMAN_EMIT_SYMBOL, 246),
    (23, HUFFMAN_EMIT_SYMBOL, 246),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 246),
    (2, HUFFMAN_EMIT_SYMBOL, 247),
    (9, HUFFMAN_EMIT_SYMBOL, 247),
    (23, HUFFMAN_EMIT_SYMBOL, 247),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 247),
    (2, HUFFMAN_EMIT_SYMBOL, 248),
    (9, HUFFMAN_EMIT_SYMBOL, 248),
    (23, HUFFMAN_EMIT_SYMBOL, 248),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 248),

    # Node 218
    (3, HUFFMAN_EMIT_SYMBOL, 245),
    (6, HUFFMAN_EMIT_SYMBOL, 245),
    (10, HUFFMAN_EMIT_SYMBOL, 245),
    (15, HUFFMAN_EMIT_SYMBOL, 245),
    (24, HUFFMAN_EMIT_SYMBOL, 245),
    (31, HUFFMAN_EMIT_SYMBOL, 245),
    (41, HUFFMAN_EMIT_SYMBOL, 245),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 245),
    (3, HUFFMAN_EMIT_SYMBOL, 246),
    (6, HUFFMAN_EMIT_SYMBOL, 246),
    (10, HUFFMAN_EMIT_SYMBOL, 246),
    (15, HUFFMAN_EMIT_SYMBOL, 246),
    (24, HUFFMAN_EMIT_SYMBOL, 246),
    (31, HUFFMAN_EMIT_SYMBOL, 246),
    (41, HUFFMAN_EMIT_SYMBOL, 246),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 246),

    # Node 219
    (3, HUFFMAN_EMIT_SYMBOL, 247),
    (6, HUFFMAN_EMIT_SYMBOL, 247),
    (10, HUFFMAN_EMIT_SYMBOL, 247),
    (15, HUFFMAN_EMIT_SYMBOL, 247),
    (24, HUFFMAN_EMIT_SYMBOL, 247),
    (31, HUFFMAN_EMIT_SYMBOL, 247),
    (41, HUFFMAN_EMIT_SYMBOL, 247),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 247),
    (3, HUFFMAN_EMIT_SYMBOL, 248),
    (6, HUFFMAN_EMIT_SYMBOL, 248),
    (10, HUFFMAN_EMIT_SYMBOL, 248),
    (15, HUFFMAN_EMIT_SYMBOL, 248),
    (24, HUFFMAN_EMIT_SYMBOL, 248),
    (31, HUFFMAN_EMIT_SYMBOL, 248),
    (41, HUFFMAN_EMIT_SYMBOL, 248),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 248),

    # Node 220
    (2, HUFFMAN_EMIT_SYMBOL, 250),
    (9, HUFFMAN_EMIT_SYMBOL, 250),
    (23, HUFFMAN_EMIT_SYMBOL, 250),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 250),
    (2, HUFFMAN_EMIT_SYMBOL, 251),
    (9, HUFFMAN_EMIT_SYMBOL, 251),
    (23, HUFFMAN_EMIT_SYMBOL, 251),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 251),
    (2, HUFFMAN_EMIT_SYMBOL, 252),
    (9, HUFFMAN_EMIT_SYMBOL, 252),
    (23, HUFFMAN_EMIT_SYMBOL, 252),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 252),
    (2, HUFFMAN_EMIT_SYMBOL, 253),
    (9, HUFFMAN_EMIT_SYMBOL, 253),
    (23, HUFFMAN_EMIT_SYMBOL, 253),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 253),

    # Node 221
    (3, HUFFMAN_EMIT_SYMBOL, 250),
    (6, HUFFMAN_EMIT_SYMBOL, 250),
    (10, HUFFMAN_EMIT_SYMBOL, 250),
    (15, HUFFMAN_EMIT_SYMBOL, 250),
    (24, HUFFMAN_EMIT_SYMBOL, 250),
    (31, HUFFMAN_EMIT_SYMBOL, 250),
    (41, HUFFMAN_EMIT_SYMBOL, 250),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 250),
    (3, HUFFMAN_EMIT_SYMBOL, 251),
    (6, HUFFMAN_EMIT_SYMBOL, 251),
    (10, HUFFMAN_EMIT_SYMBOL, 251),
    (15, HUFFMAN_EMIT_SYMBOL, 251),
    (24, HUFFMAN_EMIT_SYMBOL, 251),
    (31, HUFFMAN_EMIT_SYMBOL, 251),
    (41, HUFFMAN_EMIT_SYMBOL, 251),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 251),

    # Node 222
    (3, HUFFMAN_EMIT_SYMBOL, 252),
    (6, HUFFMAN_EMIT_SYMBOL, 252),
    (10, HUFFMAN_EMIT_SYMBOL, 252),
    (15, HUFFMAN_EMIT_SYMBOL, 252),
    (24, HUFFMAN_EMIT_SYMBOL, 252),
    (31, HUFFMAN_EMIT_SYMBOL, 252),
    (41, HUFFMAN_EMIT_SYMBOL, 252),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 252),
    (3, HUFFMAN_EMIT_SYMBOL, 253),
    (6, HUFFMAN_EMIT_SYMBOL, 253),
    (10, HUFFMAN_EMIT_SYMBOL, 253),
    (15, HUFFMAN_EMIT_SYMBOL, 253),
    (24, HUFFMAN_EMIT_SYMBOL, 253),
    (31, HUFFMAN_EMIT_SYMBOL, 253),
    (41, HUFFMAN_EMIT_SYMBOL, 253),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 253),

    # Node 223
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 254),
    (227, 0, 0),
    (229, 0, 0),
    (230, 0, 0),
    (233, 0, 0),
    (234, 0, 0),
    (236, 0, 0),
    (237, 0, 0),
    (241, 0, 0),
    (242, 0, 0),
    (244, 0, 0),
    (245, 0, 0),
    (248, 0, 0),
    (249, 0, 0),
    (251, 0, 0),
    (252, 0, 0),

    # Node 224
    (1, HUFFMAN_EMIT_SYMBOL, 254),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 254),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 2),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 3),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 4),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 5),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 6),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 7),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 8),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 11),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 12),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 14),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 15),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 16),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 17),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 18),

    # Node 225
    (2, HUFFMAN_EMIT_SYMBOL, 254),
    (9, HUFFMAN_EMIT_SYMBOL, 254),
    (23, HUFFMAN_EMIT_SYMBOL, 254),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 254),
    (1, HUFFMAN_EMIT_SYMBOL, 2),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 2),
    (1, HUFFMAN_EMIT_SYMBOL, 3),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 3),
    (1, HUFFMAN_EMIT_SYMBOL, 4),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 4),
    (1, HUFFMAN_EMIT_SYMBOL, 5),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 5),
    (1, HUFFMAN_EMIT_SYMBOL, 6),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 6),
    (1, HUFFMAN_EMIT_SYMBOL, 7),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 7),

    # Node 226
    (3, HUFFMAN_EMIT_SYMBOL, 254),
    (6, HUFFMAN_EMIT_SYMBOL, 254),
    (10, HUFFMAN_EMIT_SYMBOL, 254),
    (15, HUFFMAN_EMIT_SYMBOL, 254),
    (24, HUFFMAN_EMIT_SYMBOL, 254),
    (31, HUFFMAN_EMIT_SYMBOL, 254),
    (41, HUFFMAN_EMIT_SYMBOL, 254),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 254),
    (2, HUFFMAN_EMIT_SYMBOL, 2),
    (9, HUFFMAN_EMIT_SYMBOL, 2),
    (23, HUFFMAN_EMIT_SYMBOL, 2),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 2),
    (2, HUFFMAN_EMIT_SYMBOL, 3),
    (9, HUFFMAN_EMIT_SYMBOL, 3),
    (23, HUFFMAN_EMIT_SYMBOL, 3),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 3),

    # Node 227
    (3, HUFFMAN_EMIT_SYMBOL, 2),
    (6, HUFFMAN_EMIT_SYMBOL, 2),
    (10, HUFFMAN_EMIT_SYMBOL, 2),
    (15, HUFFMAN_EMIT_SYMBOL, 2),
    (24, HUFFMAN_EMIT_SYMBOL, 2),
    (31, HUFFMAN_EMIT_SYMBOL, 2),
    (41, HUFFMAN_EMIT_SYMBOL, 2),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 2),
    (3, HUFFMAN_EMIT_SYMBOL, 3),
    (6, HUFFMAN_EMIT_SYMBOL, 3),
    (10, HUFFMAN_EMIT_SYMBOL, 3),
    (15, HUFFMAN_EMIT_SYMBOL, 3),
    (24, HUFFMAN_EMIT_SYMBOL, 3),
    (31, HUFFMAN_EMIT_SYMBOL, 3),
    (41, HUFFMAN_EMIT_SYMBOL, 3),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 3),

    # Node 228
    (2, HUFFMAN_EMIT_SYMBOL, 4),
    (9, HUFFMAN_EMIT_SYMBOL, 4),
    (23, HUFFMAN_EMIT_SYMBOL, 4),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 4),
    (2, HUFFMAN_EMIT_SYMBOL, 5),
    (9, HUFFMAN_EMIT_SYMBOL, 5),
    (23, HUFFMAN_EMIT_SYMBOL, 5),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 5),
    (2, HUFFMAN_EMIT_SYMBOL, 6),
    (9, HUFFMAN_EMIT_SYMBOL, 6),
    (23, HUFFMAN_EMIT_SYMBOL, 6),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 6),
    (2, HUFFMAN_EMIT_SYMBOL, 7),
    (9, HUFFMAN_EMIT_SYMBOL, 7),
    (23, HUFFMAN_EMIT_SYMBOL, 7),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 7),

    # Node 229
    (3, HUFFMAN_EMIT_SYMBOL, 4),
    (6, HUFFMAN_EMIT_SYMBOL, 4),
    (10, HUFFMAN_EMIT_SYMBOL, 4),
    (15, HUFFMAN_EMIT_SYMBOL, 4),
    (24, HUFFMAN_EMIT_SYMBOL, 4),
    (31, HUFFMAN_EMIT_SYMBOL, 4),
    (41, HUFFMAN_EMIT_SYMBOL, 4),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 4),
    (3, HUFFMAN_EMIT_SYMBOL, 5),
    (6, HUFFMAN_EMIT_SYMBOL, 5),
    (10, HUFFMAN_EMIT_SYMBOL, 5),
    (15, HUFFMAN_EMIT_SYMBOL, 5),
    (24, HUFFMAN_EMIT_SYMBOL, 5),
    (31, HUFFMAN_EMIT_SYMBOL, 5),
    (41, HUFFMAN_EMIT_SYMBOL, 5),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 5),

    # Node 230
    (3, HUFFMAN_EMIT_SYMBOL, 6),
    (6, HUFFMAN_EMIT_SYMBOL, 6),
    (10, HUFFMAN_EMIT_SYMBOL, 6),
    (15, HUFFMAN_EMIT_SYMBOL, 6),
    (24, HUFFMAN_EMIT_SYMBOL, 6),
    (31, HUFFMAN_EMIT_SYMBOL, 6),
    (41, HUFFMAN_EMIT_SYMBOL, 6),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 6),
    (3, HUFFMAN_EMIT_SYMBOL, 7),
    (6, HUFFMAN_EMIT_SYMBOL, 7),
    (10, HUFFMAN_EMIT_SYMBOL, 7),
    (15, HUFFMAN_EMIT_SYMBOL, 7),
    (24, HUFFMAN_EMIT_SYMBOL, 7),
    (31, HUFFMAN_EMIT_SYMBOL, 7),
    (41, HUFFMAN_EMIT_SYMBOL, 7),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 7),

    # Node 231
    (1, HUFFMAN_EMIT_SYMBOL, 8),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 8),
    (1, HUFFMAN_EMIT_SYMBOL, 11),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 11),
    (1, HUFFMAN_EMIT_SYMBOL, 12),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 12),
    (1, HUFFMAN_EMIT_SYMBOL, 14),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 14),
    (1, HUFFMAN_EMIT_SYMBOL, 15),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 15),
    (1, HUFFMAN_EMIT_SYMBOL, 16),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 16),
    (1, HUFFMAN_EMIT_SYMBOL, 17),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 17),
    (1, HUFFMAN_EMIT_SYMBOL, 18),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 18),

    # Node 232
    (2, HUFFMAN_EMIT_SYMBOL, 8),
    (9, HUFFMAN_EMIT_SYMBOL, 8),
    (23, HUFFMAN_EMIT_SYMBOL, 8),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 8),
    (2, HUFFMAN_EMIT_SYMBOL, 11),
    (9, HUFFMAN_EMIT_SYMBOL, 11),
    (23, HUFFMAN_EMIT_SYMBOL, 11),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 11),
    (2, HUFFMAN_EMIT_SYMBOL, 12),
    (9, HUFFMAN_EMIT_SYMBOL, 12),
    (23, HUFFMAN_EMIT_SYMBOL, 12),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 12),
    (2, HUFFMAN_EMIT_SYMBOL, 14),
    (9, HUFFMAN_EMIT_SYMBOL, 14),
    (23, HUFFMAN_EMIT_SYMBOL, 14),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 14),

    # Node 233
    (3, HUFFMAN_EMIT_SYMBOL, 8),
    (6, HUFFMAN_EMIT_SYMBOL, 8),
    (10, HUFFMAN_EMIT_SYMBOL, 8),
    (15, HUFFMAN_EMIT_SYMBOL, 8),
    (24, HUFFMAN_EMIT_SYMBOL, 8),
    (31, HUFFMAN_EMIT_SYMBOL, 8),
    (41, HUFFMAN_EMIT_SYMBOL, 8),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 8),
    (3, HUFFMAN_EMIT_SYMBOL, 11),
    (6, HUFFMAN_EMIT_SYMBOL, 11),
    (10, HUFFMAN_EMIT_SYMBOL, 11),
    (15, HUFFMAN_EMIT_SYMBOL, 11),
    (24, HUFFMAN_EMIT_SYMBOL, 11),
    (31, HUFFMAN_EMIT_SYMBOL, 11),
    (41, HUFFMAN_EMIT_SYMBOL, 11),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 11),

    # Node 234
    (3, HUFFMAN_EMIT_SYMBOL, 12),
    (6, HUFFMAN_EMIT_SYMBOL, 12),
    (10, HUFFMAN_EMIT_SYMBOL, 12),
    (15, HUFFMAN_EMIT_SYMBOL, 12),
    (24, HUFFMAN_EMIT_SYMBOL, 12),
    (31, HUFFMAN_EMIT_SYMBOL, 12),
    (41, HUFFMAN_EMIT_SYMBOL, 12),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 12),
    (3, HUFFMAN_EMIT_SYMBOL, 14),
    (6, HUFFMAN_EMIT_SYMBOL, 14),
    (10, HUFFMAN_EMIT_SYMBOL, 14),
    (15, HUFFMAN_EMIT_SYMBOL, 14),
    (24, HUFFMAN_EMIT_SYMBOL, 14),
    (31, HUFFMAN_EMIT_SYMBOL, 14),
    (41, HUFFMAN_EMIT_SYMBOL, 14),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 14),

    # Node 235
    (2, HUFFMAN_EMIT_SYMBOL, 15),
    (9, HUFFMAN_EMIT_SYMBOL, 15),
    (23, HUFFMAN_EMIT_SYMBOL, 15),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 15),
    (2, HUFFMAN_EMIT_SYMBOL, 16),
    (9, HUFFMAN_EMIT_SYMBOL, 16),
    (23, HUFFMAN_EMIT_SYMBOL, 16),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 16),
    (2, HUFFMAN_EMIT_SYMBOL, 17),
    (9, HUFFMAN_EMIT_SYMBOL, 17),
    (23, HUFFMAN_EMIT_SYMBOL, 17),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 17),
    (2, HUFFMAN_EMIT_SYMBOL, 18),
    (9, HUFFMAN_EMIT_SYMBOL, 18),
    (23, HUFFMAN_EMIT_SYMBOL, 18),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 18),

    # Node 236
    (3, HUFFMAN_EMIT_SYMBOL, 15),
    (6, HUFFMAN_EMIT_SYMBOL, 15),
    (10, HUFFMAN_EMIT_SYMBOL, 15),
    (15, HUFFMAN_EMIT_SYMBOL, 15),
    (24, HUFFMAN_EMIT_SYMBOL, 15),
    (31, HUFFMAN_EMIT_SYMBOL, 15),
    (41, HUFFMAN_EMIT_SYMBOL, 15),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 15),
    (3, HUFFMAN_EMIT_SYMBOL, 16),
    (6, HUFFMAN_EMIT_SYMBOL, 16),
    (10, HUFFMAN_EMIT_SYMBOL, 16),
    (15, HUFFMAN_EMIT_SYMBOL, 16),
    (24, HUFFMAN_EMIT_SYMBOL, 16),
    (31, HUFFMAN_EMIT_SYMBOL, 16),
    (41, HUFFMAN_EMIT_SYMBOL, 16),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 16),

    # Node 237
    (3, HUFFMAN_EMIT_SYMBOL, 17),
    (6, HUFFMAN_EMIT_SYMBOL, 17),
    (10, HUFFMAN_EMIT_SYMBOL, 17),
    (15, HUFFMAN_EMIT_SYMBOL, 17),
    (24, HUFFMAN_EMIT_SYMBOL, 17),
    (31, HUFFMAN_EMIT_SYMBOL, 17),
    (41, HUFFMAN_EMIT_SYMBOL, 17),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 17),
    (3, HUFFMAN_EMIT_SYMBOL, 18),
    (6, HUFFMAN_EMIT_SYMBOL, 18),
    (10, HUFFMAN_EMIT_SYMBOL, 18),
    (15, HUFFMAN_EMIT_SYMBOL, 18),
    (24, HUFFMAN_EMIT_SYMBOL, 18),
    (31, HUFFMAN_EMIT_SYMBOL, 18),
    (41, HUFFMAN_EMIT_SYMBOL, 18),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 18),

    # Node 238
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 19),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 20),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 21),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 23),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 24),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 25),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 26),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 27),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 28),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 29),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 30),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 31),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 127),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 220),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 249),
    (253, 0, 0),

    # Node 239
    (1, HUFFMAN_EMIT_SYMBOL, 19),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 19),
    (1, HUFFMAN_EMIT_SYMBOL, 20),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 20),
    (1, HUFFMAN_EMIT_SYMBOL, 21),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 21),
    (1, HUFFMAN_EMIT_SYMBOL, 23),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 23),
    (1, HUFFMAN_EMIT_SYMBOL, 24),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 24),
    (1, HUFFMAN_EMIT_SYMBOL, 25),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 25),
    (1, HUFFMAN_EMIT_SYMBOL, 26),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 26),
    (1, HUFFMAN_EMIT_SYMBOL, 27),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 27),

    # Node 240
    (2, HUFFMAN_EMIT_SYMBOL, 19),
    (9, HUFFMAN_EMIT_SYMBOL, 19),
    (23, HUFFMAN_EMIT_SYMBOL, 19),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 19),
    (2, HUFFMAN_EMIT_SYMBOL, 20),
    (9, HUFFMAN_EMIT_SYMBOL, 20),
    (23, HUFFMAN_EMIT_SYMBOL, 20),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 20),
    (2, HUFFMAN_EMIT_SYMBOL, 21),
    (9, HUFFMAN_EMIT_SYMBOL, 21),
    (23, HUFFMAN_EMIT_SYMBOL, 21),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 21),
    (2, HUFFMAN_EMIT_SYMBOL, 23),
    (9, HUFFMAN_EMIT_SYMBOL, 23),
    (23, HUFFMAN_EMIT_SYMBOL, 23),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 23),

    # Node 241
    (3, HUFFMAN_EMIT_SYMBOL, 19),
    (6, HUFFMAN_EMIT_SYMBOL, 19),
    (10, HUFFMAN_EMIT_SYMBOL, 19),
    (15, HUFFMAN_EMIT_SYMBOL, 19),
    (24, HUFFMAN_EMIT_SYMBOL, 19),
    (31, HUFFMAN_EMIT_SYMBOL, 19),
    (41, HUFFMAN_EMIT_SYMBOL, 19),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 19),
    (3, HUFFMAN_EMIT_SYMBOL, 20),
    (6, HUFFMAN_EMIT_SYMBOL, 20),
    (10, HUFFMAN_EMIT_SYMBOL, 20),
    (15, HUFFMAN_EMIT_SYMBOL, 20),
    (24, HUFFMAN_EMIT_SYMBOL, 20),
    (31, HUFFMAN_EMIT_SYMBOL, 20),
    (41, HUFFMAN_EMIT_SYMBOL, 20),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 20),

    # Node 242
    (3, HUFFMAN_EMIT_SYMBOL, 21),
    (6, HUFFMAN_EMIT_SYMBOL, 21),
    (10, HUFFMAN_EMIT_SYMBOL, 21),
    (15, HUFFMAN_EMIT_SYMBOL, 21),
    (24, HUFFMAN_EMIT_SYMBOL, 21),
    (31, HUFFMAN_EMIT_SYMBOL, 21),
    (41, HUFFMAN_EMIT_SYMBOL, 21),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 21),
    (3, HUFFMAN_EMIT_SYMBOL, 23),
    (6, HUFFMAN_EMIT_SYMBOL, 23),
    (10, HUFFMAN_EMIT_SYMBOL, 23),
    (15, HUFFMAN_EMIT_SYMBOL, 23),
    (24, HUFFMAN_EMIT_SYMBOL, 23),
    (31, HUFFMAN_EMIT_SYMBOL, 23),
    (41, HUFFMAN_EMIT_SYMBOL, 23),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 23),

    # Node 243
    (2, HUFFMAN_EMIT_SYMBOL, 24),
    (9, HUFFMAN_EMIT_SYMBOL, 24),
    (23, HUFFMAN_EMIT_SYMBOL, 24),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 24),
    (2, HUFFMAN_EMIT_SYMBOL, 25),
    (9, HUFFMAN_EMIT_SYMBOL, 25),
    (23, HUFFMAN_EMIT_SYMBOL, 25),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 25),
    (2, HUFFMAN_EMIT_SYMBOL, 26),
    (9, HUFFMAN_EMIT_SYMBOL, 26),
    (23, HUFFMAN_EMIT_SYMBOL, 26),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 26),
    (2, HUFFMAN_EMIT_SYMBOL, 27),
    (9, HUFFMAN_EMIT_SYMBOL, 27),
    (23, HUFFMAN_EMIT_SYMBOL, 27),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 27),

    # Node 244
    (3, HUFFMAN_EMIT_SYMBOL, 24),
    (6, HUFFMAN_EMIT_SYMBOL, 24),
    (10, HUFFMAN_EMIT_SYMBOL, 24),
    (15, HUFFMAN_EMIT_SYMBOL, 24),
    (24, HUFFMAN_EMIT_SYMBOL, 24),
    (31, HUFFMAN_EMIT_SYMBOL, 24),
    (41, HUFFMAN_EMIT_SYMBOL, 24),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 24),
    (3, HUFFMAN_EMIT_SYMBOL, 25),
    (6, HUFFMAN_EMIT_SYMBOL, 25),
    (10, HUFFMAN_EMIT_SYMBOL, 25),
    (15, HUFFMAN_EMIT_SYMBOL, 25),
    (24, HUFFMAN_EMIT_SYMBOL, 25),
    (31, HUFFMAN_EMIT_SYMBOL, 25),
    (41, HUFFMAN_EMIT_SYMBOL, 25),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 25),

    # Node 245
    (3, HUFFMAN_EMIT_SYMBOL, 26),
    (6, HUFFMAN_EMIT_SYMBOL, 26),
    (10, HUFFMAN_EMIT_SYMBOL, 26),
    (15, HUFFMAN_EMIT_SYMBOL, 26),
    (24, HUFFMAN_EMIT_SYMBOL, 26),
    (31, HUFFMAN_EMIT_SYMBOL, 26),
    (41, HUFFMAN_EMIT_SYMBOL, 26),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 26),
    (3, HUFFMAN_EMIT_SYMBOL, 27),
    (6, HUFFMAN_EMIT_SYMBOL, 27),
    (10, HUFFMAN_EMIT_SYMBOL, 27),
    (15, HUFFMAN_EMIT_SYMBOL, 27),
    (24, HUFFMAN_EMIT_SYMBOL, 27),
    (31, HUFFMAN_EMIT_SYMBOL, 27),
    (41, HUFFMAN_EMIT_SYMBOL, 27),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 27),

    # Node 246
    (1, HUFFMAN_EMIT_SYMBOL, 28),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 28),
    (1, HUFFMAN_EMIT_SYMBOL, 29),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 29),
    (1, HUFFMAN_EMIT_SYMBOL, 30),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 30),
    (1, HUFFMAN_EMIT_SYMBOL, 31),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 31),
    (1, HUFFMAN_EMIT_SYMBOL, 127),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 127),
    (1, HUFFMAN_EMIT_SYMBOL, 220),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 220),
    (1, HUFFMAN_EMIT_SYMBOL, 249),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 249),
    (254, 0, 0),
    (255, 0, 0),

    # Node 247
    (2, HUFFMAN_EMIT_SYMBOL, 28),
    (9, HUFFMAN_EMIT_SYMBOL, 28),
    (23, HUFFMAN_EMIT_SYMBOL, 28),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 28),
    (2, HUFFMAN_EMIT_SYMBOL, 29),
    (9, HUFFMAN_EMIT_SYMBOL, 29),
    (23, HUFFMAN_EMIT_SYMBOL, 29),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 29),
    (2, HUFFMAN_EMIT_SYMBOL, 30),
    (9, HUFFMAN_EMIT_SYMBOL, 30),
    (23, HUFFMAN_EMIT_SYMBOL, 30),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 30),
    (2, HUFFMAN_EMIT_SYMBOL, 31),
    (9, HUFFMAN_EMIT_SYMBOL, 31),
    (23, HUFFMAN_EMIT_SYMBOL, 31),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 31),

    # Node 248
    (3, HUFFMAN_EMIT_SYMBOL, 28),
    (6, HUFFMAN_EMIT_SYMBOL, 28),
    (10, HUFFMAN_EMIT_SYMBOL, 28),
    (15, HUFFMAN_EMIT_SYMBOL, 28),
    (24, HUFFMAN_EMIT_SYMBOL, 28),
    (31, HUFFMAN_EMIT_SYMBOL, 28),
    (41, HUFFMAN_EMIT_SYMBOL, 28),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 28),
    (3, HUFFMAN_EMIT_SYMBOL, 29),
    (6, HUFFMAN_EMIT_SYMBOL, 29),
    (10, HUFFMAN_EMIT_SYMBOL, 29),
    (15, HUFFMAN_EMIT_SYMBOL, 29),
    (24, HUFFMAN_EMIT_SYMBOL, 29),
    (31, HUFFMAN_EMIT_SYMBOL, 29),
    (41, HUFFMAN_EMIT_SYMBOL, 29),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 29),

    # Node 249
    (3, HUFFMAN_EMIT_SYMBOL, 30),
    (6, HUFFMAN_EMIT_SYMBOL, 30),
    (10, HUFFMAN_EMIT_SYMBOL, 30),
    (15, HUFFMAN_EMIT_SYMBOL, 30),
    (24, HUFFMAN_EMIT_SYMBOL, 30),
    (31, HUFFMAN_EMIT_SYMBOL, 30),
    (41, HUFFMAN_EMIT_SYMBOL, 30),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 30),
    (3, HUFFMAN_EMIT_SYMBOL, 31),
    (6, HUFFMAN_EMIT_SYMBOL, 31),
    (10, HUFFMAN_EMIT_SYMBOL, 31),
    (15, HUFFMAN_EMIT_SYMBOL, 31),
    (24, HUFFMAN_EMIT_SYMBOL, 31),
    (31, HUFFMAN_EMIT_SYMBOL, 31),
    (41, HUFFMAN_EMIT_SYMBOL, 31),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 31),

    # Node 250
    (2, HUFFMAN_EMIT_SYMBOL, 127),
    (9, HUFFMAN_EMIT_SYMBOL, 127),
    (23, HUFFMAN_EMIT_SYMBOL, 127),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 127),
    (2, HUFFMAN_EMIT_SYMBOL, 220),
    (9, HUFFMAN_EMIT_SYMBOL, 220),
    (23, HUFFMAN_EMIT_SYMBOL, 220),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 220),
    (2, HUFFMAN_EMIT_SYMBOL, 249),
    (9, HUFFMAN_EMIT_SYMBOL, 249),
    (23, HUFFMAN_EMIT_SYMBOL, 249),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 249),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 10),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 13),
    (0, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 22),
    (0, HUFFMAN_FAIL, 0),

    # Node 251
    (3, HUFFMAN_EMIT_SYMBOL, 127),
    (6, HUFFMAN_EMIT_SYMBOL, 127),
    (10, HUFFMAN_EMIT_SYMBOL, 127),
    (15, HUFFMAN_EMIT_SYMBOL, 127),
    (24, HUFFMAN_EMIT_SYMBOL, 127),
    (31, HUFFMAN_EMIT_SYMBOL, 127),
    (41, HUFFMAN_EMIT_SYMBOL, 127),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 127),
    (3, HUFFMAN_EMIT_SYMBOL, 220),
    (6, HUFFMAN_EMIT_SYMBOL, 220),
    (10, HUFFMAN_EMIT_SYMBOL, 220),
    (15, HUFFMAN_EMIT_SYMBOL, 220),
    (24, HUFFMAN_EMIT_SYMBOL, 220),
    (31, HUFFMAN_EMIT_SYMBOL, 220),
    (41, HUFFMAN_EMIT_SYMBOL, 220),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 220),

    # Node 252
    (3, HUFFMAN_EMIT_SYMBOL, 249),
    (6, HUFFMAN_EMIT_SYMBOL, 249),
    (10, HUFFMAN_EMIT_SYMBOL, 249),
    (15, HUFFMAN_EMIT_SYMBOL, 249),
    (24, HUFFMAN_EMIT_SYMBOL, 249),
    (31, HUFFMAN_EMIT_SYMBOL, 249),
    (41, HUFFMAN_EMIT_SYMBOL, 249),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 249),
    (1, HUFFMAN_EMIT_SYMBOL, 10),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 10),
    (1, HUFFMAN_EMIT_SYMBOL, 13),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 13),
    (1, HUFFMAN_EMIT_SYMBOL, 22),
    (22, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 22),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),

    # Node 253
    (2, HUFFMAN_EMIT_SYMBOL, 10),
    (9, HUFFMAN_EMIT_SYMBOL, 10),
    (23, HUFFMAN_EMIT_SYMBOL, 10),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 10),
    (2, HUFFMAN_EMIT_SYMBOL, 13),
    (9, HUFFMAN_EMIT_SYMBOL, 13),
    (23, HUFFMAN_EMIT_SYMBOL, 13),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 13),
    (2, HUFFMAN_EMIT_SYMBOL, 22),
    (9, HUFFMAN_EMIT_SYMBOL, 22),
    (23, HUFFMAN_EMIT_SYMBOL, 22),
    (40, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 22),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),

    # Node 254
    (3, HUFFMAN_EMIT_SYMBOL, 10),
    (6, HUFFMAN_EMIT_SYMBOL, 10),
    (10, HUFFMAN_EMIT_SYMBOL, 10),
    (15, HUFFMAN_EMIT_SYMBOL, 10),
    (24, HUFFMAN_EMIT_SYMBOL, 10),
    (31, HUFFMAN_EMIT_SYMBOL, 10),
    (41, HUFFMAN_EMIT_SYMBOL, 10),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 10),
    (3, HUFFMAN_EMIT_SYMBOL, 13),
    (6, HUFFMAN_EMIT_SYMBOL, 13),
    (10, HUFFMAN_EMIT_SYMBOL, 13),
    (15, HUFFMAN_EMIT_SYMBOL, 13),
    (24, HUFFMAN_EMIT_SYMBOL, 13),
    (31, HUFFMAN_EMIT_SYMBOL, 13),
    (41, HUFFMAN_EMIT_SYMBOL, 13),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 13),

    # Node 255
    (3, HUFFMAN_EMIT_SYMBOL, 22),
    (6, HUFFMAN_EMIT_SYMBOL, 22),
    (10, HUFFMAN_EMIT_SYMBOL, 22),
    (15, HUFFMAN_EMIT_SYMBOL, 22),
    (24, HUFFMAN_EMIT_SYMBOL, 22),
    (31, HUFFMAN_EMIT_SYMBOL, 22),
    (41, HUFFMAN_EMIT_SYMBOL, 22),
    (56, HUFFMAN_COMPLETE | HUFFMAN_EMIT_SYMBOL, 22),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
    (0, HUFFMAN_FAIL, 0),
]
