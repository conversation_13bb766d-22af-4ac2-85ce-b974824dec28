Metadata-Version: 2.1
Name: llama-index-core
Version: 0.10.57
Summary: Interface between LLMs and your data
Home-page: https://llamaindex.ai
License: MIT
Keywords: LLM,NLP,RAG,data,devtools,index,retrieval
Author: <PERSON>-email: <EMAIL>
Maintainer: <PERSON>-email: <EMAIL>
Requires-Python: >=3.8.1,<4.0
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Dist: PyYAML (>=6.0.1)
Requires-Dist: SQLAlchemy[asyncio] (>=1.4.49)
Requires-Dist: aiohttp (>=3.8.6,<4.0.0)
Requires-Dist: dataclasses-json
Requires-Dist: deprecated (>=1.2.9.3)
Requires-Dist: dirty<PERSON>son (>=1.0.8,<2.0.0)
Requires-Dist: fsspec (>=2023.5.0)
Requires-Dist: httpx
Requires-Dist: nest-asyncio (>=1.5.8,<2.0.0)
Requires-Dist: networkx (>=3.0)
Requires-Dist: nltk (>=3.8.1,<4.0.0)
Requires-Dist: numpy (<2.0.0)
Requires-Dist: openai (>=1.1.0)
Requires-Dist: pandas
Requires-Dist: pillow (>=9.0.0)
Requires-Dist: requests (>=2.31.0)
Requires-Dist: tenacity (>=8.2.0,!=8.4.0,<9.0.0)
Requires-Dist: tiktoken (>=0.3.3)
Requires-Dist: tqdm (>=4.66.1,<5.0.0)
Requires-Dist: typing-extensions (>=4.5.0)
Requires-Dist: typing-inspect (>=0.8.0)
Requires-Dist: wrapt
Project-URL: Documentation, https://docs.llamaindex.ai/en/stable/
Project-URL: Repository, https://github.com/run-llama/llama_index
Description-Content-Type: text/markdown

# LlamaIndex Core

The core python package to the LlamaIndex library. Core classes and abstractions
represent the foundational building blocks for LLM applications, most notably,
RAG. Such building blocks include abstractions for LLMs, Vector Stores, Embeddings,
Storage, Callables and several others.

We've designed the core library so that it can be easily extended through subclasses.
Building LLM applications with LlamaIndex thus involves building with LlamaIndex
core as well as with the LlamaIndex [integrations](https://github.com/run-llama/llama_index/tree/main/llama-index-integrations) needed for your application.

