#!/usr/bin/env python3
"""测试 Qdrant 客户端连接"""

import sys
import traceback

def test_qdrant_client():
    """测试官方 QdrantClient"""
    try:
        from qdrant_client import QdrantClient
        print("✓ qdrant_client 导入成功")
        
        # 测试不同的连接方式
        connection_methods = [
            ("URL方式", lambda: QdrantClient(url="http://localhost:6333", prefer_grpc=False)),
            ("Host/Port方式", lambda: QdrantClient(host="localhost", port=6333, prefer_grpc=False)),
            ("Host/Port + timeout", lambda: QdrantClient(host="localhost", port=6333, prefer_grpc=False, timeout=10)),
        ]
        
        for method_name, client_factory in connection_methods:
            try:
                print(f"\n测试 {method_name}:")
                client = client_factory()
                print(f"  ✓ 客户端创建成功")
                
                # 测试连接
                collections = client.get_collections()
                print(f"  ✓ 连接测试成功，集合数量: {len(collections.collections)}")
                
                # 测试 collection_exists 方法
                exists = client.collection_exists("hybsearchdoc")
                print(f"  ✓ collection_exists 方法可用: {exists}")
                
                return client  # 返回第一个成功的客户端
                
            except Exception as e:
                print(f"  ✗ {method_name} 失败: {e}")
                traceback.print_exc()
        
        return None
        
    except ImportError as e:
        print(f"✗ qdrant_client 导入失败: {e}")
        return None

def test_requests():
    """测试 requests 直接访问"""
    try:
        import requests
        print("\n测试 requests 直接访问:")
        
        response = requests.get("http://localhost:6333/collections", timeout=5)
        print(f"  ✓ HTTP 状态码: {response.status_code}")
        print(f"  ✓ 响应内容: {response.text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"  ✗ requests 访问失败: {e}")
        return False

if __name__ == "__main__":
    print("=== Qdrant 连接测试 ===")
    
    # 测试 requests
    requests_ok = test_requests()
    
    # 测试官方客户端
    client = test_qdrant_client()
    
    print(f"\n=== 测试结果 ===")
    print(f"Requests 访问: {'✓' if requests_ok else '✗'}")
    print(f"官方客户端: {'✓' if client else '✗'}")
    
    if client:
        print("建议使用官方 QdrantClient")
    elif requests_ok:
        print("建议使用 SimpleQdrantClient (REST API)")
    else:
        print("Qdrant 连接完全失败")
